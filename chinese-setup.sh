#!/bin/bash

# n8n 中文化设置脚本
# 这个脚本会在n8n容器内设置中文环境

set -e

echo "🇨🇳 开始设置 n8n 中文化..."

# 检查n8n服务是否运行
if ! docker-compose ps | grep -q "n8n_main.*Up"; then
    echo "❌ n8n 服务未运行，请先启动服务"
    exit 1
fi

echo "📦 安装中文语言包..."

# 在容器内安装中文语言包
docker-compose exec n8n sh -c "
    # 更新包管理器
    apk update
    
    # 安装中文字体和语言包
    apk add --no-cache \
        font-noto-cjk \
        font-noto-emoji \
        icu-data-full \
        tzdata
    
    # 设置中文环境变量
    export LANG=zh_CN.UTF-8
    export LC_ALL=zh_CN.UTF-8
    export N8N_DEFAULT_LOCALE=zh-CN
    
    echo '✅ 中文语言包安装完成'
"

echo "🔄 重启 n8n 服务以应用中文设置..."
docker-compose restart n8n

# 等待服务重启
echo "⏳ 等待服务重启..."
sleep 15

# 检查服务状态
for i in {1..30}; do
    if curl -s http://localhost:5678/healthz > /dev/null 2>&1; then
        echo "✅ n8n 服务重启成功！"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "⚠️  n8n 服务重启超时，请检查日志"
        docker-compose logs n8n
        exit 1
    fi
    echo "等待中... ($i/30)"
    sleep 2
done

echo ""
echo "🎉 n8n 中文化设置完成！"
echo ""
echo "📋 设置信息："
echo "   🌐 访问地址: http://localhost:5678"
echo "   🇨🇳 默认语言: 中文 (zh-CN)"
echo "   🕐 时区: Asia/Shanghai"
echo ""
echo "💡 提示："
echo "   1. 如果界面仍显示英文，请清除浏览器缓存后刷新"
echo "   2. 可以在用户设置中手动切换语言"
echo "   3. 部分第三方节点可能仍显示英文"
echo ""
echo "🔗 现在可以访问 http://localhost:5678 查看中文界面"
