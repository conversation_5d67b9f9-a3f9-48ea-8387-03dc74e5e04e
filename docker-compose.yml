version: '3.8'

services:
  # PostgreSQL 数据库服务
  postgres:
    image: postgres:15
    container_name: n8n_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: n8n
      POSTGRES_USER: n8n
      POSTGRES_PASSWORD: n8n_password
      POSTGRES_NON_ROOT_USER: n8n
      POSTGRES_NON_ROOT_PASSWORD: n8n_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-data.sh:/docker-entrypoint-initdb.d/init-data.sh
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -h localhost -U n8n -d n8n']
      interval: 5s
      timeout: 5s
      retries: 10
    networks:
      - n8n_network

  # Redis 服务 (用于队列模式)
  redis:
    image: redis:7-alpine
    container_name: n8n_redis
    restart: unless-stopped
    volumes:
      - redis_data:/data
    healthcheck:
      test: ['CMD', 'redis-cli', 'ping']
      interval: 5s
      timeout: 3s
      retries: 5
    networks:
      - n8n_network

  # n8n 主服务
  n8n:
    image: docker.n8n.io/n8nio/n8n:latest
    container_name: n8n_main
    restart: unless-stopped
    ports:
      - "5678:5678"
    environment:
      # 数据库配置
      DB_TYPE: postgresdb
      DB_POSTGRESDB_DATABASE: n8n
      DB_POSTGRESDB_HOST: postgres
      DB_POSTGRESDB_PORT: 5432
      DB_POSTGRESDB_USER: n8n
      DB_POSTGRESDB_PASSWORD: n8n_password
      DB_POSTGRESDB_SCHEMA: public
      
      # Redis 配置 (队列模式)
      QUEUE_BULL_REDIS_HOST: redis
      QUEUE_BULL_REDIS_PORT: 6379
      EXECUTIONS_MODE: queue
      
      # 基本配置
      N8N_HOST: localhost
      N8N_PORT: 5678
      N8N_PROTOCOL: http
      WEBHOOK_URL: http://localhost:5678/
      
      # 时区设置
      GENERIC_TIMEZONE: Asia/Shanghai
      TZ: Asia/Shanghai

      # 语言设置 - 中文支持
      N8N_DEFAULT_LOCALE: zh-CN
      LANG: zh_CN.UTF-8
      LC_ALL: zh_CN.UTF-8
      
      # 安全配置
      N8N_ENCRYPTION_KEY: ${N8N_ENCRYPTION_KEY:-your-encryption-key-here}
      
      # 日志配置
      N8N_LOG_LEVEL: info
      N8N_LOG_OUTPUT: console
      
      # 工作流配置
      WORKFLOWS_DEFAULT_NAME: "My Workflow"
      N8N_DEFAULT_BINARY_DATA_MODE: filesystem
      
      # 用户管理
      N8N_USER_MANAGEMENT_DISABLED: false
      
      # 指标和监控
      N8N_METRICS: true
      
      # 禁用遥测
      N8N_DIAGNOSTICS_ENABLED: false
      N8N_VERSION_NOTIFICATIONS_ENABLED: false
      N8N_TEMPLATES_ENABLED: true
      
      # 外部存储配置 (可选)
      N8N_BINARY_DATA_TTL: 168
      N8N_BINARY_DATA_STORAGE_PATH: /home/<USER>/.n8n/binaryData
      
    volumes:
      # 持久化数据目录
      - n8n_data:/home/<USER>/.n8n
      # 自定义节点目录
      - ./custom_nodes:/home/<USER>/.n8n/custom
      # 备份目录
      - ./backups:/home/<USER>/backups
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ['CMD-SHELL', 'wget --no-verbose --tries=1 --spider http://localhost:5678/healthz || exit 1']
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - n8n_network

volumes:
  # PostgreSQL 数据持久化
  postgres_data:
    driver: local
  
  # Redis 数据持久化
  redis_data:
    driver: local
  
  # n8n 数据持久化
  n8n_data:
    driver: local

networks:
  n8n_network:
    driver: bridge
