#!/bin/bash

# n8n Docker 部署启动脚本
# 这个脚本会帮助你快速启动 n8n 服务

set -e

echo "🚀 开始部署 n8n..."

# 检查 Docker 是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

# 检查 Docker Compose 是否安装
if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

# 检查 .env 文件是否存在
if [ ! -f .env ]; then
    echo "⚠️  .env 文件不存在，正在创建默认配置..."
    cp .env .env.backup 2>/dev/null || true
fi

# 生成随机加密密钥
if grep -q "your-super-secret-encryption-key-change-this-please" .env 2>/dev/null; then
    echo "🔑 生成新的加密密钥..."
    ENCRYPTION_KEY=$(openssl rand -hex 32)
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        sed -i '' "s/your-super-secret-encryption-key-change-this-please/$ENCRYPTION_KEY/" .env
    else
        # Linux
        sed -i "s/your-super-secret-encryption-key-change-this-please/$ENCRYPTION_KEY/" .env
    fi
    echo "✅ 加密密钥已生成并保存到 .env 文件"
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p custom_nodes
mkdir -p backups

# 拉取最新镜像
echo "📥 拉取最新的 Docker 镜像..."
docker-compose pull

# 启动服务
echo "🔄 启动 n8n 服务..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose ps

# 等待 n8n 服务完全启动
echo "⏳ 等待 n8n 服务完全启动..."
for i in {1..30}; do
    if curl -s http://localhost:5678/healthz > /dev/null 2>&1; then
        echo "✅ n8n 服务已成功启动！"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "⚠️  n8n 服务启动超时，请检查日志"
        docker-compose logs n8n
        exit 1
    fi
    echo "等待中... ($i/30)"
    sleep 2
done

echo ""
echo "🎉 n8n 部署完成！"
echo ""
echo "📋 服务信息："
echo "   🌐 n8n Web 界面: http://localhost:5678"
echo "   🗄️  PostgreSQL: localhost:5432"
echo "   🔴 Redis: localhost:6379"
echo ""
echo "📁 数据目录："
echo "   📝 自定义节点: ./custom_nodes/"
echo "   💾 备份目录: ./backups/"
echo ""
echo "🛠️  常用命令："
echo "   查看日志: docker-compose logs -f n8n"
echo "   停止服务: docker-compose down"
echo "   重启服务: docker-compose restart"
echo ""
echo "⚠️  重要提醒："
echo "   1. 首次访问需要创建管理员账户"
echo "   2. 请妥善保管 .env 文件中的加密密钥"
echo "   3. 建议定期备份数据"
echo ""
echo "🔗 更多信息请查看 README.md 文件"
