#!/bin/bash

# n8n 数据备份脚本
# 用于备份 n8n 的工作流、凭据和数据库

set -e

# 配置
BACKUP_DIR="./backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="n8n_backup_$DATE"

echo "🔄 开始备份 n8n 数据..."

# 创建备份目录
mkdir -p "$BACKUP_DIR/$BACKUP_NAME"

# 检查服务是否运行
if ! docker-compose ps | grep -q "Up"; then
    echo "❌ n8n 服务未运行，请先启动服务"
    exit 1
fi

echo "📁 创建备份目录: $BACKUP_DIR/$BACKUP_NAME"

# 1. 备份 PostgreSQL 数据库
echo "🗄️  备份 PostgreSQL 数据库..."
docker-compose exec -T postgres pg_dump -U n8n n8n > "$BACKUP_DIR/$BACKUP_NAME/database.sql"
if [ $? -eq 0 ]; then
    echo "✅ 数据库备份完成"
else
    echo "❌ 数据库备份失败"
    exit 1
fi

# 2. 备份工作流
echo "📋 备份工作流..."
docker-compose exec n8n n8n export:workflow --backup --output="/home/<USER>/backups/$BACKUP_NAME/"
if [ $? -eq 0 ]; then
    echo "✅ 工作流备份完成"
else
    echo "⚠️  工作流备份可能失败（如果没有工作流则正常）"
fi

# 3. 备份凭据
echo "🔐 备份凭据..."
docker-compose exec n8n n8n export:credentials --backup --output="/home/<USER>/backups/$BACKUP_NAME/"
if [ $? -eq 0 ]; then
    echo "✅ 凭据备份完成"
else
    echo "⚠️  凭据备份可能失败（如果没有凭据则正常）"
fi

# 4. 备份配置文件
echo "⚙️  备份配置文件..."
cp .env "$BACKUP_DIR/$BACKUP_NAME/env_backup"
cp docker-compose.yml "$BACKUP_DIR/$BACKUP_NAME/"
echo "✅ 配置文件备份完成"

# 5. 备份自定义节点
if [ -d "custom_nodes" ] && [ "$(ls -A custom_nodes)" ]; then
    echo "🔧 备份自定义节点..."
    cp -r custom_nodes "$BACKUP_DIR/$BACKUP_NAME/"
    echo "✅ 自定义节点备份完成"
else
    echo "ℹ️  没有自定义节点需要备份"
fi

# 6. 创建备份信息文件
echo "📝 创建备份信息文件..."
cat > "$BACKUP_DIR/$BACKUP_NAME/backup_info.txt" << EOF
n8n 备份信息
=============

备份时间: $(date)
备份名称: $BACKUP_NAME
备份内容:
- PostgreSQL 数据库 (database.sql)
- 工作流文件
- 凭据文件
- 环境配置 (env_backup)
- Docker Compose 配置 (docker-compose.yml)
- 自定义节点 (如果存在)

恢复说明:
1. 恢复数据库: docker-compose exec -T postgres psql -U n8n n8n < database.sql
2. 恢复工作流: docker-compose exec n8n n8n import:workflow --input=/home/<USER>/backups/$BACKUP_NAME/
3. 恢复凭据: docker-compose exec n8n n8n import:credentials --input=/home/<USER>/backups/$BACKUP_NAME/
4. 恢复配置: 复制 env_backup 到 .env 文件
5. 恢复自定义节点: 复制 custom_nodes 目录

注意: 恢复前请确保服务已停止，恢复后重启服务
EOF

# 7. 压缩备份文件 (可选)
echo "📦 压缩备份文件..."
cd "$BACKUP_DIR"
tar -czf "${BACKUP_NAME}.tar.gz" "$BACKUP_NAME"
if [ $? -eq 0 ]; then
    echo "✅ 备份文件已压缩: ${BACKUP_NAME}.tar.gz"
    # 删除未压缩的目录
    rm -rf "$BACKUP_NAME"
else
    echo "⚠️  压缩失败，保留原始备份目录"
fi
cd - > /dev/null

# 8. 清理旧备份 (保留最近10个)
echo "🧹 清理旧备份文件..."
cd "$BACKUP_DIR"
ls -t n8n_backup_*.tar.gz 2>/dev/null | tail -n +11 | xargs rm -f 2>/dev/null || true
BACKUP_COUNT=$(ls -1 n8n_backup_*.tar.gz 2>/dev/null | wc -l)
echo "📊 当前保留 $BACKUP_COUNT 个备份文件"
cd - > /dev/null

echo ""
echo "🎉 备份完成！"
echo "📁 备份位置: $BACKUP_DIR/${BACKUP_NAME}.tar.gz"
echo "📋 备份内容: 数据库、工作流、凭据、配置文件"
echo ""
echo "💡 提示："
echo "   - 建议定期运行此脚本进行备份"
echo "   - 可以设置 cron 任务自动备份"
echo "   - 重要数据请额外保存到其他位置"
