#!/bin/bash

# n8n 部署验证脚本
# 验证 Docker 部署和中文化是否成功

set -e

echo "🔍 开始验证 n8n 部署状态..."
echo ""

# 1. 检查Docker服务状态
echo "📦 检查 Docker 服务状态："
docker-compose ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}"
echo ""

# 2. 检查服务健康状态
echo "🏥 检查服务健康状态："

# 检查 n8n
if curl -s http://localhost:5678/healthz | grep -q "ok"; then
    echo "✅ n8n 服务: 健康"
else
    echo "❌ n8n 服务: 异常"
fi

# 检查 PostgreSQL
if docker-compose exec postgres pg_isready -U n8n -d n8n > /dev/null 2>&1; then
    echo "✅ PostgreSQL: 健康"
else
    echo "❌ PostgreSQL: 异常"
fi

# 检查 Redis
if docker-compose exec redis redis-cli ping | grep -q "PONG"; then
    echo "✅ Redis: 健康"
else
    echo "❌ Redis: 异常"
fi

echo ""

# 3. 检查数据持久化
echo "💾 检查数据持久化："
echo "📁 Docker 卷："
docker volume ls | grep n8n || echo "⚠️  未找到 n8n 相关卷"

echo ""
echo "📂 本地目录："
if [ -d "./custom_nodes" ]; then
    echo "✅ 自定义节点目录: ./custom_nodes/"
else
    echo "❌ 自定义节点目录: 不存在"
fi

if [ -d "./backups" ]; then
    echo "✅ 备份目录: ./backups/"
else
    echo "❌ 备份目录: 不存在"
fi

echo ""

# 4. 检查中文化设置
echo "🇨🇳 检查中文化设置："
if grep -q "N8N_DEFAULT_LOCALE=zh-CN" .env; then
    echo "✅ 默认语言: zh-CN"
else
    echo "❌ 默认语言: 未设置"
fi

if grep -q "GENERIC_TIMEZONE=Asia/Shanghai" .env; then
    echo "✅ 时区设置: Asia/Shanghai"
else
    echo "❌ 时区设置: 未设置"
fi

echo ""

# 5. 检查网络连接
echo "🌐 检查网络连接："
if curl -s http://localhost:5678 > /dev/null; then
    echo "✅ Web 界面: http://localhost:5678 可访问"
else
    echo "❌ Web 界面: 无法访问"
fi

echo ""

# 6. 显示版本信息
echo "📋 版本信息："
N8N_VERSION=$(docker-compose exec n8n n8n --version 2>/dev/null | grep -o '[0-9]\+\.[0-9]\+\.[0-9]\+' | head -1)
if [ ! -z "$N8N_VERSION" ]; then
    echo "✅ n8n 版本: $N8N_VERSION"
else
    echo "❌ 无法获取 n8n 版本"
fi

echo ""

# 7. 显示资源使用情况
echo "📊 资源使用情况："
echo "🐳 Docker 容器资源："
docker stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}" | grep n8n

echo ""

# 8. 总结
echo "📝 部署总结："
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "🎉 n8n Docker 部署完成！"
echo ""
echo "📋 功能特性："
echo "   ✅ 最新版本 n8n"
echo "   ✅ PostgreSQL 数据库"
echo "   ✅ Redis 队列支持"
echo "   ✅ 数据持久化"
echo "   ✅ 中文界面支持"
echo "   ✅ 自动重启策略"
echo "   ✅ 健康检查"
echo ""
echo "🔗 访问信息："
echo "   🌐 Web 界面: http://localhost:5678"
echo "   🗄️  数据库: localhost:5432"
echo "   🔴 缓存: localhost:6379"
echo ""
echo "🛠️  管理命令："
echo "   启动: docker-compose up -d"
echo "   停止: docker-compose down"
echo "   重启: docker-compose restart"
echo "   日志: docker-compose logs -f n8n"
echo "   备份: ./backup.sh"
echo ""
echo "💡 下一步："
echo "   1. 访问 http://localhost:5678"
echo "   2. 使用已配置的管理员账户登录"
echo "   3. 开始创建自动化工作流"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
