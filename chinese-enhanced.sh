#!/bin/bash

# n8n 增强中文化脚本
# 通过环境变量和浏览器设置实现中文化

set -e

echo "🇨🇳 开始增强 n8n 中文化设置..."

# 检查n8n服务是否运行
if ! docker-compose ps | grep -q "n8n_main.*Up"; then
    echo "❌ n8n 服务未运行，请先启动服务"
    exit 1
fi

echo "⚙️  更新环境变量配置..."

# 确保环境变量包含中文设置
if ! grep -q "N8N_DEFAULT_LOCALE=zh-CN" .env; then
    echo "N8N_DEFAULT_LOCALE=zh-CN" >> .env
fi

if ! grep -q "LANG=zh_CN.UTF-8" .env; then
    echo "LANG=zh_CN.UTF-8" >> .env
fi

if ! grep -q "LC_ALL=zh_CN.UTF-8" .env; then
    echo "LC_ALL=zh_CN.UTF-8" >> .env
fi

echo "🔄 重启 n8n 服务以应用设置..."
docker-compose restart n8n

# 等待服务重启
echo "⏳ 等待服务重启..."
sleep 15

# 检查服务状态
for i in {1..30}; do
    if curl -s http://localhost:5678/healthz > /dev/null 2>&1; then
        echo "✅ n8n 服务重启成功！"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "⚠️  n8n 服务重启超时，请检查日志"
        docker-compose logs n8n
        exit 1
    fi
    echo "等待中... ($i/30)"
    sleep 2
done

echo ""
echo "🎉 n8n 增强中文化设置完成！"
echo ""
echo "📋 当前配置："
echo "   🌐 访问地址: http://localhost:5678"
echo "   🇨🇳 默认语言: zh-CN"
echo "   🕐 时区: Asia/Shanghai"
echo "   📦 数据持久化: ✅"
echo "   🔄 队列模式: ✅"
echo ""
echo "🔧 中文化说明："
echo "   1. 已设置中文环境变量"
echo "   2. 浏览器语言设置为中文时会自动显示中文"
echo "   3. 可在用户设置中手动切换语言"
echo "   4. 清除浏览器缓存可能有助于显示中文"
echo ""
echo "💡 使用提示："
echo "   - 首次访问需要创建管理员账户"
echo "   - 在设置中可以切换界面语言"
echo "   - 工作流节点大部分支持中文显示"
echo ""
echo "🔗 现在可以访问 http://localhost:5678 开始使用中文版 n8n"
