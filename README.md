# n8n Docker 部署指南

这是一个完整的 n8n Docker 部署配置，包含 PostgreSQL 数据库和 Redis 缓存，确保数据持久化。

## 🚀 快速开始

### 1. 准备工作

确保你的系统已安装：
- Docker
- Docker Compose

### 2. 配置环境变量

复制并编辑环境变量文件：
```bash
cp .env.example .env
```

**重要：** 请务必修改 `.env` 文件中的 `N8N_ENCRYPTION_KEY`，设置为一个强随机字符串。

### 3. 启动服务

```bash
# 启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f n8n

# 查看所有服务状态
docker-compose ps
```

### 4. 访问 n8n

打开浏览器访问：http://localhost:5678

首次访问时，你需要创建管理员账户。

## 📁 目录结构

```
.
├── docker-compose.yml    # Docker Compose 配置文件
├── .env                  # 环境变量配置
├── init-data.sh         # PostgreSQL 初始化脚本
├── custom_nodes/        # 自定义节点目录
├── backups/             # 备份目录
└── README.md            # 说明文档
```

## 🔧 服务说明

### n8n 主服务
- **端口**: 5678
- **数据持久化**: `/home/<USER>/.n8n` 目录
- **自定义节点**: `./custom_nodes` 目录映射
- **备份**: `./backups` 目录映射

### PostgreSQL 数据库
- **版本**: PostgreSQL 15
- **数据库名**: n8n
- **用户名**: n8n
- **数据持久化**: `postgres_data` 卷

### Redis 缓存
- **版本**: Redis 7 Alpine
- **用途**: 队列模式支持
- **数据持久化**: `redis_data` 卷

## 🛠️ 常用命令

### 服务管理
```bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 查看日志
docker-compose logs -f [service_name]

# 更新到最新版本
docker-compose pull
docker-compose down
docker-compose up -d
```

### 数据备份
```bash
# 备份 PostgreSQL 数据库
docker-compose exec postgres pg_dump -U n8n n8n > backups/n8n_backup_$(date +%Y%m%d_%H%M%S).sql

# 备份 n8n 配置和工作流
docker-compose exec n8n n8n export:workflow --backup --output=/home/<USER>/backups/

# 备份 n8n 凭据
docker-compose exec n8n n8n export:credentials --backup --output=/home/<USER>/backups/
```

### 数据恢复
```bash
# 恢复 PostgreSQL 数据库
docker-compose exec -T postgres psql -U n8n n8n < backups/your_backup.sql

# 恢复工作流
docker-compose exec n8n n8n import:workflow --input=/home/<USER>/backups/

# 恢复凭据
docker-compose exec n8n n8n import:credentials --input=/home/<USER>/backups/
```

## 🔒 安全建议

1. **更改默认密码**: 修改 `.env` 文件中的数据库密码
2. **设置强加密密钥**: 确保 `N8N_ENCRYPTION_KEY` 是一个强随机字符串
3. **网络安全**: 如果部署到生产环境，考虑使用反向代理和 HTTPS
4. **定期备份**: 设置定期备份计划
5. **更新维护**: 定期更新 Docker 镜像

## 🌐 生产环境部署

如果要部署到生产环境，建议：

1. 使用域名和 HTTPS
2. 配置反向代理 (Nginx/Traefik)
3. 设置防火墙规则
4. 配置监控和日志收集
5. 设置自动备份

## 📝 自定义节点

将自定义节点放在 `custom_nodes/` 目录下，n8n 会自动加载。

## 🐛 故障排除

### 常见问题

1. **端口冲突**: 如果 5678 端口被占用，修改 docker-compose.yml 中的端口映射
2. **权限问题**: 确保 `init-data.sh` 有执行权限：`chmod +x init-data.sh`
3. **数据库连接失败**: 检查 PostgreSQL 服务是否正常启动
4. **内存不足**: 确保系统有足够的内存运行所有服务

### 查看日志
```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs n8n
docker-compose logs postgres
docker-compose logs redis
```

## 📞 支持

如果遇到问题，可以：
1. 查看 [n8n 官方文档](https://docs.n8n.io/)
2. 访问 [n8n 社区论坛](https://community.n8n.io/)
3. 查看 [GitHub Issues](https://github.com/n8n-io/n8n/issues)

## 📄 许可证

本配置文件遵循 MIT 许可证。n8n 本身遵循 [Fair-code license](https://docs.n8n.io/reference/license/)。
