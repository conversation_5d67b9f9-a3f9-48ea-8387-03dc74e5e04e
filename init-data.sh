#!/bin/bash
set -e

# 初始化 PostgreSQL 数据库脚本
# 这个脚本会在 PostgreSQL 容器首次启动时运行

echo "开始初始化 n8n 数据库..."

# 创建 n8n 数据库 (如果不存在)
psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
    -- 确保数据库存在
    SELECT 'CREATE DATABASE n8n'
    WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'n8n')\gexec
    
    -- 创建扩展 (如果需要)
    CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
    
    -- 设置默认权限
    GRANT ALL PRIVILEGES ON DATABASE n8n TO n8n;
EOSQL

echo "n8n 数据库初始化完成!"
