<!DOCTYPE html>
<html lang="en">
	<head>
		<script type="module" crossorigin src="/{{BASE_PATH}}/assets/polyfills-eMEFRc79.js"></script>

		<meta charset="utf-8" />
		<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="viewport" content="width=device-width,initial-scale=1.0" />
		<link rel="icon" href="/{{BASE_PATH}}/favicon.ico" />
		<style>@media (prefers-color-scheme: dark) { body { background-color: rgb(45, 46, 46) } }</style>
		<script src="/{{REST_ENDPOINT}}/config.js"></script>
		<script>!function(t,e){var o,n,p,r;e.__SV||(window.posthog=e,e._i=[],e.init=function(i,s,a){function g(t,e){var o=e.split(".");2==o.length&&(t=t[o[0]],e=o[1]),t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}}(p=t.createElement("script")).type="text/javascript",p.async=!0,p.src=s.api_host+"/static/array.js",(r=t.getElementsByTagName("script")[0]).parentNode.insertBefore(p,r);var u=e;for(void 0!==a?u=e[a]=[]:a="posthog",u.people=u.people||[],u.toString=function(t){var e="posthog";return"posthog"!==a&&(e+="."+a),t||(e+=" (stub)"),e},u.people.toString=function(){return u.toString(1)+".people (stub)"},o="capture identify alias people.set people.set_once set_config register register_once unregister opt_out_capturing has_opted_out_capturing opt_in_capturing reset isFeatureEnabled getFeatureFlag onFeatureFlags reloadFeatureFlags".split(" "),n=0;n<o.length;n++)g(u,o[n]);e._i.push([i,s,a])},e.__SV=1)}(document,window.posthog||[])</script>

		<title>n8n.io - Workflow Automation</title>
		<script type="module" crossorigin src="/{{BASE_PATH}}/assets/index-DS4Zr0kd.js"></script>
		<link rel="stylesheet" crossorigin href="/{{BASE_PATH}}/assets/index-BWpYwmXd.css">
	</head>
	<body>
		<noscript>
			<strong
				>We're sorry but the n8n Editor-UI doesn't work properly without JavaScript enabled. Please
				enable it to continue.</strong
			>
		</noscript>
		<div id="app"></div>
	</body>
</html>
