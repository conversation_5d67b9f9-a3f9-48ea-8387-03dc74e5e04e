/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._cardLink_ni149_123 {
  transition: box-shadow 0.3s ease;
  cursor: pointer;
  padding: 0 0 0 var(--spacing-s);
  align-items: stretch;
}
._cardLink_ni149_123:hover {
  box-shadow: 0 2px 8px rgba(68, 28, 23, 0.1);
}
._cardHeading_ni149_133 {
  font-size: var(--font-size-s);
  word-break: word-break;
  padding: var(--spacing-s) 0 0 var(--spacing-s);
  width: 200px;
}
._cardDescription_ni149_140 {
  min-height: 19px;
  display: flex;
  align-items: center;
  padding: 0 0 var(--spacing-s) var(--spacing-s);
}
._cardActions_ni149_147 {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 0 var(--spacing-s) 0 0;
  cursor: default;
}
._cardApiKey_ni149_156 {
  flex-grow: 1;
  display: flex;
  justify-content: center;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._header_1hiim_123 {
  display: flex;
  align-items: center;
  white-space: nowrap;
  margin-bottom: var(--spacing-xl);
}
._header_1hiim_123 *:first-child {
  flex-grow: 1;
}
._card_1hiim_133 {
  position: relative;
}
._destinationItem_1hiim_137 {
  margin-bottom: var(--spacing-2xs);
}
._delete_1hiim_141 {
  position: absolute;
  display: inline-block;
  top: var(--spacing-s);
  right: var(--spacing-s);
}
._topHint_1hiim_148 {
  margin-top: none;
  margin-bottom: var(--spacing-s);
  color: var(--color-text-light);
}
._topHint_1hiim_148 span {
  font-size: var(--font-size-s);
  line-height: var(--font-line-height-loose);
  font-weight: var(--font-weight-regular);
}
._BottomHint_1hiim_159 {
  margin-bottom: var(--spacing-s);
  margin-top: var(--spacing-s);
}
._apiKeysContainer_1hiim_164 {
  max-height: 45vh;
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: none;
}