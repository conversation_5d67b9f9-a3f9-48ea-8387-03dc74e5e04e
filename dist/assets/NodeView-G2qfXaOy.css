/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._iconButton_fx9xi_123 {
  padding-left: 0;
  padding-right: 0;
}
._iconButton_fx9xi_123 svg {
  width: 16px;
  height: 16px;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
.vue-flow__controls {
  display: flex;
  gap: var(--spacing-xs);
  box-shadow: none;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._edge_cbk94_123 {
  transition-property: stroke, opacity;
  transition-duration: 300ms;
  transition-timing-function: ease;
  opacity: 0;
}
._edge_cbk94_123._visible_cbk94_129 {
  opacity: 1;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._contextMenu_12fz3_123 {
  position: fixed;
}
._activator_12fz3_127 {
  pointer-events: none;
  opacity: 0;
}
path[data-v-efb5a8d2] {
	stroke: var(--color-canvas-read-only-line);
}
/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._canvasEdgeToolbar_uogmx_123 {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-2xs);
  pointer-events: all;
  padding: var(--spacing-2xs);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
@media (prefers-color-scheme: dark) {
body:not([data-theme]) .canvas-edge-toolbar-button {
    --button-background-color: var(--color-background-base);
    --button-hover-background-color: var(--color-background-light);
}
}
[data-theme=dark] .canvas-edge-toolbar-button {
  --button-background-color: var(--color-background-base);
  --button-hover-background-color: var(--color-background-light);
}
.canvas-edge-toolbar-button {
  border-width: 2px;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._edge_fyzmo_123 {
  transition: stroke 0.3s ease, fill 0.3s ease;
}
._edgeLabelWrapper_fyzmo_127 {
  transform: translateY(calc(var(--spacing-xs) * -1));
  position: absolute;
}
._edgeLabel_fyzmo_127 {
  font-size: var(--font-size-xs);
  background-color: hsla(var(--color-canvas-background-h), var(--color-canvas-background-s), var(--color-canvas-background-l), 0.85);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._popover_3lp7u_123 {
  min-width: 208px;
  margin-bottom: -8px;
  margin-left: -2px;
}
._content_3lp7u_129 {
  display: flex;
  flex-direction: row;
  width: -moz-fit-content;
  width: fit-content;
  gap: var(--spacing-2xs);
}
._color_3lp7u_136 {
  width: 20px;
  height: 20px;
  border-width: 1px;
  border-style: solid;
  border-color: var(--color-foreground-xdark);
  border-radius: 50%;
  background: var(--color-sticky-background);
}
._color_3lp7u_136:hover {
  cursor: pointer;
}
._color_3lp7u_136._selected_3lp7u_148 {
  box-shadow: 0 0 0 1px var(--color-sticky-background);
}
._color_3lp7u_136._sticky-color-1_3lp7u_151 {
  --color-sticky-background: var(--color-sticky-background-1);
}
._color_3lp7u_136._sticky-color-2_3lp7u_154 {
  --color-sticky-background: var(--color-sticky-background-2);
}
._color_3lp7u_136._sticky-color-3_3lp7u_157 {
  --color-sticky-background: var(--color-sticky-background-3);
}
._color_3lp7u_136._sticky-color-4_3lp7u_160 {
  --color-sticky-background: var(--color-sticky-background-4);
}
._color_3lp7u_136._sticky-color-5_3lp7u_163 {
  --color-sticky-background: var(--color-sticky-background-5);
}
._color_3lp7u_136._sticky-color-6_3lp7u_166 {
  --color-sticky-background: var(--color-sticky-background-6);
}
._color_3lp7u_136._sticky-color-7_3lp7u_169 {
  --color-sticky-background: var(--color-sticky-background-7);
}
._option_3lp7u_173 {
  display: inline-block;
  padding: var(--spacing-3xs);
  color: var(--color-text-light);
}
._option_3lp7u_173 svg {
  width: var(--font-size-s) !important;
}
._option_3lp7u_173:hover {
  color: var(--color-primary);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._canvasNodeToolbar_104hp_123 {
  padding-bottom: var(--spacing-xs);
  display: flex;
  justify-content: flex-end;
  width: 100%;
}
._canvasNodeToolbarItems_104hp_130 {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-canvas-background);
  border-radius: var(--border-radius-base);
}
._canvasNodeToolbarItems_104hp_130 .button {
  --button-font-color: var(--color-text-light);
}
._forceVisible_104hp_141 {
  opacity: 1 !important;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._status_93vv6_123 {
  display: flex;
  align-items: center;
  gap: var(--spacing-5xs);
  font-weight: var(--font-weight-bold);
}
._runData_93vv6_130 {
  color: var(--color-success);
}
._waiting_93vv6_134 {
  color: var(--color-secondary);
}
._pinnedData_93vv6_138 {
  color: var(--color-secondary);
}
._running_93vv6_142 {
  width: calc(100% - 2 * var(--canvas-node--status-icons-offset));
  height: calc(100% - 2 * var(--canvas-node--status-icons-offset));
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3.75em;
  color: hsla(var(--color-primary-h), var(--color-primary-s), var(--color-primary-l), 0.7);
}
._node-waiting-spinner_93vv6_152 {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3.75em;
  color: hsla(var(--color-primary-h), var(--color-primary-s), var(--color-primary-l), 0.7);
  width: 100%;
  height: 100%;
  position: absolute;
  left: -34px;
  top: -34px;
}
._issues_93vv6_165 {
  color: var(--color-danger);
  cursor: default;
}
._count_93vv6_170 {
  font-size: var(--font-size-s);
}
._warning_93vv6_174 {
  color: var(--color-warning);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._disabledStrikeThrough_nl2g7_123 {
  border: 1px solid var(--color-foreground-dark);
  position: absolute;
  top: calc(var(--canvas-node--height) / 2 - 1px);
  left: -4px;
  width: calc(100% + 12px);
  pointer-events: none;
}
._success_nl2g7_132 {
  border-color: var(--color-success-light);
}
._warning_nl2g7_136 {
  border-color: var(--color-warning-tint-1);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._tooltipTrigger_dauyi_123 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
._popper_dauyi_131 {
  white-space: nowrap;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._select_wv0ev_123 {
  --max-select-width: 224px;
  max-width: var(--max-select-width);
}
._select_wv0ev_123 .el-input--suffix .el-input__inner {
  padding-left: calc(var(--spacing-l) + var(--spacing-4xs));
  padding-right: var(--spacing-l);
}
._node_wv0ev_132 {
  --select-option-padding: 0 var(--spacing-s);
  display: flex;
  align-items: center;
  font-size: var(--font-size-2xs);
  gap: var(--spacing-4xs);
}
._icon_wv0ev_140 {
  padding-right: var(--spacing-4xs);
}
._title_wv0ev_144 {
  color: var(--color-text-dark);
  font-weight: var(--font-weight-regular);
  max-width: var(--max-select-width);
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
._disabled_wv0ev_153 ._title_wv0ev_144 {
  color: var(--color-text-light);
}
._subtitle_wv0ev_157 {
  margin-left: auto;
  padding-left: var(--spacing-2xs);
  color: var(--color-text-light);
  font-weight: var(--font-weight-regular);
}
._empty_1x72k_2 {
	display: flex;
	flex-flow: column;
	align-items: center;
	justify-content: center;
	gap: var(--spacing-2xs);

	line-height: 2;
	color: var(--color-text-base);
}
._title_1x72k_13 {
	font-size: var(--font-size-m);
	font-weight: var(--font-weight-bold);
	color: var(--color-text-base);
	margin: 0;
}
._description_1x72k_20 {
	font-size: var(--font-size-s);
	max-width: 180px;
	margin: 0;
}
/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._runData_1mq3a_123 {
  background-color: var(--color-run-data-background);
}
._mappedNode_1mq3a_127 {
  padding: 0 var(--spacing-s) var(--spacing-s);
}
._titleSection_1mq3a_131 {
  display: flex;
  max-width: 300px;
  align-items: center;
}
._titleSection_1mq3a_131 > * {
  margin-right: var(--spacing-2xs);
}
._titleSectionV2_1mq3a_140 {
  padding-left: var(--spacing-4xs);
}
._inputModeTab_1mq3a_144 {
  margin-left: auto;
}
._noOutputData_1mq3a_148 {
  max-width: 250px;
}
._noOutputData_1mq3a_148 > * {
  margin-bottom: var(--spacing-2xs);
}
._recoveredOutputData_1mq3a_155 {
  margin: auto;
  max-width: 250px;
  text-align: center;
}
._recoveredOutputData_1mq3a_155 > *:first-child {
  margin-bottom: var(--spacing-m);
}
._notConnected_1mq3a_164 {
  max-width: 300px;
}
._notConnected_1mq3a_164 > *:first-child {
  margin-bottom: var(--spacing-m);
}
._notConnected_1mq3a_164 > * {
  margin-bottom: var(--spacing-2xs);
}
._title_1mq3a_131 {
  text-transform: uppercase;
  letter-spacing: 3px;
}
._titleV2_1mq3a_179 {
  letter-spacing: 2px;
  font-size: var(--font-size-xs);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._container_178ym_123 {
  font-weight: var(--font-weight-medium);
  display: flex;
  font-size: var(--font-size-m);
  margin-right: var(--spacing-s);
  color: var(--color-text-dark);
  width: 100%;
}
._textWrapper_178ym_132 {
  display: flex;
  flex-grow: 1;
}
._iconWrapper_178ym_137 {
  display: inline-flex;
  margin-right: var(--spacing-2xs);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._container_1lo1n_123 {
  display: flex;
  justify-content: right;
  align-items: center;
  gap: var(--spacing-2xs);
  padding: var(--spacing-s);
}
._separator_1lo1n_131 {
  height: var(--border-width-base);
  background: var(--color-foreground-base);
  margin-right: var(--spacing-s);
  margin-left: var(--spacing-s);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
#communityNode > div {
  cursor: auto;
  padding-right: 0;
  padding-left: 0;
}
#communityNode > div:hover {
  color: unset;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
.webhooks[data-v-0dd27b5e] {
  padding-bottom: var(--spacing-xs);
  margin: var(--spacing-xs) 0;
  border-bottom: 1px solid var(--color-text-lighter);
}
.webhooks .headline[data-v-0dd27b5e] {
  color: var(--color-primary);
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-2xs);
}
.http-field[data-v-0dd27b5e] {
  position: absolute;
  width: 50px;
  display: inline-block;
  top: calc(50% - 8px);
}
.http-method[data-v-0dd27b5e] {
  background-color: var(--color-foreground-xdark);
  width: 40px;
  height: 16px;
  line-height: 16px;
  margin-left: 5px;
  text-align: center;
  border-radius: 2px;
  font-size: var(--font-size-2xs);
  font-weight: var(--font-weight-bold);
  color: var(--color-foreground-xlight);
}
.minimize-icon[data-v-0dd27b5e] {
  font-size: 1.3em;
  margin-right: 0.5em;
}
.mode-selection-headline[data-v-0dd27b5e] {
  line-height: 1.8em;
}
.node-webhooks[data-v-0dd27b5e] {
  margin-left: 1em;
}
.url-field[data-v-0dd27b5e] {
  display: inline-block;
  width: calc(100% - 60px);
  margin-left: 55px;
}
.url-field-full-width[data-v-0dd27b5e] {
  display: inline-block;
  margin: 5px 10px;
}
.url-selection[data-v-0dd27b5e] {
  margin-top: var(--spacing-xs);
}
.minimize-button[data-v-0dd27b5e] {
  display: inline-block;
  transition-duration: 0.5s;
  transition-property: transform;
}
.expanded .minimize-button[data-v-0dd27b5e] {
  transform: rotate(90deg);
}
.webhook-url[data-v-0dd27b5e] {
  position: relative;
  top: 0;
  width: 100%;
  font-size: var(--font-size-2xs);
  white-space: normal;
  overflow: visible;
  text-overflow: initial;
  color: var(--color-text-dark);
  text-align: left;
  direction: ltr;
  word-break: break-all;
}
.webhook-wrapper[data-v-0dd27b5e] {
  line-height: 1.5;
  position: relative;
  margin-top: var(--spacing-xs);
  background-color: var(--color-foreground-xlight);
  border-radius: 3px;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
@keyframes _horizontal-shake_ac466_1 {
0% {
    transform: translateX(0);
}
25% {
    transform: translateX(5px);
}
50% {
    transform: translateX(-5px);
}
75% {
    transform: translateX(5px);
}
100% {
    transform: translateX(0);
}
}
._container_ac466_140 {
  --node-size: 45px;
  --plus-button-size: 30px;
  --animation-duration: 150ms;
  --collapsed-offset: 10px;
  padding-top: calc(var(--node-size) + var(--spacing-3xs));
}
._connections_ac466_148 {
  min-height: calc(var(--node-size) + var(--spacing-m));
  position: absolute;
  bottom: calc(var(--node-size) / 2 * -1);
  left: 0;
  right: 0;
  -webkit-user-select: none;
          user-select: none;
  justify-content: space-between;
  display: grid;
  grid-template-columns: repeat(var(--possible-connections), 1fr);
}
._connectionType_ac466_160 {
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all calc(var(--animation-duration) - 50ms) ease;
}
._connectionLabel_ac466_167 {
  margin-bottom: var(--spacing-2xs);
  font-size: var(--font-size-2xs);
  -webkit-user-select: none;
          user-select: none;
  text-wrap: nowrap;
}
._connectionLabel_ac466_167._hasIssues_ac466_173 {
  color: var(--color-danger);
}
._connectedNodesWrapper_ac466_177 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  position: relative;
}
._plusButton_ac466_185 {
  transition: all var(--animation-duration) ease;
  position: absolute;
  top: var(--spacing-2xs);
}
._plusButton_ac466_185._hasIssues_ac466_173 {
  animation: _horizontal-shake_ac466_1 500ms;
}
._plusButton_ac466_185._hasIssues_ac466_173 button {
  --button-font-color: var(--color-danger);
  --button-border-color: var(--color-danger);
}
._plusButton_ac466_185:not(:last-child) {
  z-index: 1;
  right: 100%;
  margin-right: calc(var(--plus-button-size) * -1 * 0.9);
  pointer-events: none;
}
._connectedNodesWrapperExpanded_ac466_203 ._plusButton_ac466_185:not(:last-child) {
  margin-right: var(--spacing-2xs);
  opacity: 1;
  pointer-events: all;
}
._connectedNodesMultiple_ac466_209 {
  transition: all var(--animation-duration) ease;
}
._connectedNodesWrapperExpanded_ac466_203 {
  z-index: 1;
}
._connections_ac466_148:has(._connectedNodesWrapperExpanded_ac466_203) ._connectionType_ac466_160:not(:has(._connectedNodesWrapperExpanded_ac466_203)) {
  opacity: 0;
  pointer-events: none;
  visibility: hidden;
}
._connectedNode_ac466_177 {
  border: var(--border-base);
  background-color: var(--color-node-background);
  border-radius: 100%;
  padding: var(--spacing-xs);
  cursor: pointer;
  pointer-events: all;
  transition: all var(--animation-duration) ease;
  position: relative;
  display: flex;
  justify-self: center;
  align-self: center;
}
._connectedNodes_ac466_177 {
  display: flex;
  justify-content: center;
  margin-right: calc((var(--nodes-length) - 1) * -1 * (var(--node-size) - var(--collapsed-offset)));
}
._connectedNodesWrapperExpanded_ac466_203 ._connectedNodes_ac466_177 {
  margin-right: 0;
  margin-right: calc((var(--spacing-2xs) + var(--plus-button-size)) * -1);
}
._nodeWrapper_ac466_247 {
  transition: all var(--animation-duration) ease;
  transform-origin: center;
  z-index: 1;
}
._connectedNodesWrapperExpanded_ac466_203 ._nodeWrapper_ac466_247:not(:first-child) {
  margin-left: var(--spacing-2xs);
}
._nodeWrapper_ac466_247._hasIssues_ac466_173 ._connectedNode_ac466_177 {
  border-width: calc(var(--border-width-base) * 2);
  border-color: var(--color-danger);
}
._nodeWrapper_ac466_247:not(:first-child) {
  transform: translateX(calc(var(--node-index) * -1 * (var(--node-size) - var(--collapsed-offset))));
}
._connectedNodesWrapperExpanded_ac466_203 ._nodeWrapper_ac466_247 {
  transform: translateX(0);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._header_1mro3_123 {
  --spacing-bottom-tab: calc(var(--spacing-xs));
  --font-size-tab: var(--font-size-2xs);
  --color-tabs-arrow-buttons: var(--color-background-xlight);
  --font-weight-tab: var(--font-weight-bold);
  display: flex;
  align-items: center;
  min-height: 40px;
  padding-right: var(--spacing-s);
  border-bottom: var(--border-base);
}
._tabs_1mro3_135 {
  padding-top: calc(var(--spacing-xs) + 1px);
  height: 100%;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._header_1vh2c_123 {
  background-color: var(--color-background-base);
}
._warningIcon_1vh2c_127 {
  color: var(--color-text-lighter);
  font-size: var(--font-size-2xl);
}
._descriptionContainer_1vh2c_132 {
  display: flex;
  flex-direction: column;
}
._featureRequest_1vh2c_137 {
  margin-top: auto;
  align-self: center;
}
._featureRequest_1vh2c_137 a {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-4xs);
  margin-top: var(--spacing-xl);
  font-size: var(--font-size-3xs);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-light);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
.node-settings[data-v-b0fee41c] {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: var(--color-background-xlight);
  height: 100%;
  width: 100%;
}
.node-settings .no-parameters[data-v-b0fee41c] {
  margin-top: var(--spacing-xs);
}
.node-settings .header-side-menu[data-v-b0fee41c] {
  padding: var(--spacing-s) var(--spacing-s) var(--spacing-s) var(--spacing-s);
  font-size: var(--font-size-l);
  display: flex;
  justify-content: space-between;
}
.node-settings .header-side-menu .node-name[data-v-b0fee41c] {
  padding-top: var(--spacing-5xs);
}
.node-settings.embedded .header-side-menu[data-v-b0fee41c] {
  padding: var(--spacing-xs);
}
.node-settings .node-is-not-valid[data-v-b0fee41c] {
  height: 75%;
  padding: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  line-height: var(--font-line-height-regular);
}
.node-settings .node-parameters-wrapper[data-v-b0fee41c] {
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  padding: 0 var(--spacing-m) var(--spacing-l) var(--spacing-m);
  flex-grow: 1;
}
.node-settings .node-parameters-wrapper.ndv-v2[data-v-b0fee41c] {
  padding: 0 var(--spacing-s) var(--spacing-l) var(--spacing-s);
}
.node-settings.embedded .node-parameters-wrapper[data-v-b0fee41c] {
  padding: 0 var(--spacing-xs) var(--spacing-xs) var(--spacing-xs);
}
.node-settings.embedded .node-parameters-wrapper.with-static-scrollbar[data-v-b0fee41c] {
  padding: 0 var(--spacing-2xs) var(--spacing-xs) var(--spacing-xs);
}
@supports not (selector(::-webkit-scrollbar)) {
.node-settings.embedded .node-parameters-wrapper.with-static-scrollbar[data-v-b0fee41c] {
    scrollbar-width: thin;
}
}
@supports selector(::-webkit-scrollbar) {
.node-settings.embedded .node-parameters-wrapper.with-static-scrollbar[data-v-b0fee41c]::-webkit-scrollbar {
    width: var(--spacing-2xs);
}
.node-settings.embedded .node-parameters-wrapper.with-static-scrollbar[data-v-b0fee41c]::-webkit-scrollbar-thumb {
    border-radius: var(--spacing-2xs);
    background: var(--color-foreground-dark);
    border: var(--spacing-5xs) solid var(--color-background-xlight);
}
}
.node-settings.dragging[data-v-b0fee41c] {
  border-color: var(--color-primary);
  box-shadow: 0px 6px 16px rgba(255, 74, 51, 0.15);
}
.parameter-content[data-v-b0fee41c] {
  font-size: 0.9em;
  margin-right: -15px;
  margin-left: -15px;
}
.parameter-content input[data-v-b0fee41c] {
  width: calc(100% - 35px);
  padding: 5px;
}
.parameter-content select[data-v-b0fee41c] {
  width: calc(100% - 20px);
  padding: 5px;
}
.parameter-content[data-v-b0fee41c]:before {
  display: table;
  content: " ";
  position: relative;
  box-sizing: border-box;
  clear: both;
}
.parameter-wrapper[data-v-b0fee41c] {
  padding: 0 1em;
}
.color-reset-button-wrapper[data-v-b0fee41c] {
  position: relative;
}
.color-reset-button[data-v-b0fee41c] {
  position: absolute;
  right: 7px;
  top: -25px;
}
.node-version[data-v-b0fee41c] {
  border-top: var(--border-base);
  font-size: var(--font-size-xs);
  font-size: var(--font-size-2xs);
  padding: var(--spacing-xs) 0 var(--spacing-2xs) 0;
  color: var(--color-text-light);
}
.parameter-value input.expression[data-v-b0fee41c] {
  border-style: dashed;
  border-color: #ff9600;
  display: inline-block;
  position: relative;
  width: 100%;
  box-sizing: border-box;
  background-color: #793300;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
:root ._component_1fv8q_123 {
  position: relative;
  align-items: flex-start;
  justify-content: stretch;
  border-width: 1px !important;
  border-radius: var(--border-radius-base) !important;
  width: calc(var(--canvas-node--width) * var(--node-width-scaler));
}
:root ._component_1fv8q_123._expanded_1fv8q_131 {
  cursor: default;
  height: auto;
  max-height: min(var(--canvas-node--height) * 2, 300px);
  min-height: var(--spacing-3xl);
}
:root ._component_1fv8q_123._collapsed_1fv8q_137 {
  overflow: hidden;
  height: var(--spacing-3xl);
}
:root .vue-flow__node:has(._component_1fv8q_123) {
  z-index: 10;
}
.selected :root .vue-flow__node:has(._component_1fv8q_123) {
  z-index: 11;
}
:root ._collapsedContent_1fv8q_149,
:root ._settingsView_1fv8q_150 {
  z-index: 1000;
  width: 100%;
  border-radius: var(--border-radius-base);
  height: auto;
  max-height: calc(min(var(--canvas-node--height) * 2, 300px) - var(--border-width-base) * 2);
  min-height: var(--spacing-3xl);
}
._collapsedContent_1fv8q_149 {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-s);
  background-color: white;
  padding: var(--spacing-2xs);
  background-color: var(--color-background-xlight);
  color: var(--color-text-base);
  cursor: pointer;
}
._collapsedContent_1fv8q_149 > * {
  zoom: calc(var(--zoom) * 1.25);
}
._settingsView_1fv8q_150 > * {
  zoom: var(--zoom);
}
._inputPanelContainer_1fv8q_179 {
  position: absolute;
  right: 100%;
  top: 0;
  padding-right: var(--spacing-4xs);
  margin-top: calc(-1 * var(--border-width-base));
  width: 180px;
  z-index: 2000;
  max-height: 80vh;
}
._inputPanel_1fv8q_179 {
  border: var(--border-base);
  border-width: 1px;
  background-color: var(--color-background-light);
  border-radius: var(--border-radius-large);
  zoom: var(--zoom);
  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.05);
  padding: var(--spacing-2xs);
  height: 100%;
}
._inputPanelTitle_1fv8q_201 {
  text-transform: uppercase;
  letter-spacing: 3px;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
.input-enter-active[data-v-7a0aa493],
.input-leave-active[data-v-7a0aa493] {
  transition: opacity 0.3s ease;
}
.input-enter-from[data-v-7a0aa493],
.input-leave-to[data-v-7a0aa493] {
  opacity: 0;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._node_15pfo_123 {
  --canvas-node-border-width: 2px;
  --trigger-node--border-radius: 36px;
  --canvas-node--status-icons-offset: var(--spacing-3xs);
  --node-icon-color: var(--color-foreground-dark);
  position: relative;
  height: var(--canvas-node--height);
  width: var(--canvas-node--width);
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--canvas-node--background, var(--color-node-background));
  border: var(--canvas-node-border-width) solid var(--canvas-node--border-color, var(--color-foreground-xdark));
  border-radius: var(--border-radius-large);
  /**
   * Node types
   */
  /**
   * State classes
   * The reverse order defines the priority in case multiple states are active
   */
}
._node_15pfo_123._trigger_15pfo_145 {
  border-radius: var(--trigger-node--border-radius) var(--border-radius-large) var(--border-radius-large) var(--trigger-node--border-radius);
}
._node_15pfo_123._configuration_15pfo_148 {
  background: var(--canvas-node--background, var(--node-type-supplemental-background));
  border: var(--canvas-node-border-width) solid var(--canvas-node--border-color, var(--color-foreground-dark));
  border-radius: calc(var(--canvas-node--height) / 2);
}
._node_15pfo_123._configuration_15pfo_148 ._statusIcons_15pfo_153 {
  right: unset;
}
._node_15pfo_123._configurable_15pfo_156 ._icon_15pfo_156 {
  margin-left: calc(40px - (var(--node-icon-size)) / 2 - var(--canvas-node-border-width));
}
._node_15pfo_123._configurable_15pfo_156 ._description_15pfo_159 {
  top: unset;
  position: relative;
  margin-top: 0;
  margin-left: var(--spacing-s);
  margin-right: var(--spacing-s);
  width: auto;
  min-width: unset;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-grow: 1;
  flex-shrink: 1;
}
._node_15pfo_123._configurable_15pfo_156 ._label_15pfo_172 {
  text-align: left;
}
._node_15pfo_123._configurable_15pfo_156 ._subtitle_15pfo_175 {
  text-align: left;
}
._node_15pfo_123._configurable_15pfo_156._configuration_15pfo_148 ._icon_15pfo_156 {
  margin-left: calc((var(--canvas-node--height) - var(--node-icon-size) - 4px) / 2);
}
._node_15pfo_123._configurable_15pfo_156._configuration_15pfo_148:not(._running_15pfo_181) ._statusIcons_15pfo_153 {
  position: static;
  margin-right: var(--spacing-2xs);
}
._node_15pfo_123._configurable_15pfo_156._configuration_15pfo_148 ._description_15pfo_159 {
  margin-right: var(--spacing-xs);
}
._node_15pfo_123._selected_15pfo_188 {
  box-shadow: 0 0 0 8px var(--color-canvas-selected-transparent);
}
._node_15pfo_123._success_15pfo_191 {
  --canvas-node--border-color: var(
  	--color-canvas-node-success-border-color,
  	var(--color-success)
  );
}
._node_15pfo_123._warning_15pfo_197 {
  --canvas-node--border-color: var(--color-warning);
}
._node_15pfo_123._error_15pfo_200 {
  --canvas-node--border-color: var(--color-canvas-node-error-border-color, var(--color-danger));
}
._node_15pfo_123._pinned_15pfo_203 {
  --canvas-node--border-color: var(
  	--color-canvas-node-pinned-border-color,
  	var(--color-node-pinned-border)
  );
}
._node_15pfo_123._disabled_15pfo_209 {
  --canvas-node--border-color: var(
  	--color-canvas-node-disabled-border-color,
  	var(--color-foreground-base)
  );
}
._node_15pfo_123._running_15pfo_181 {
  background-color: var(--color-node-executing-background);
  --canvas-node--border-color: var(
  	--color-canvas-node-running-border-color,
  	var(--color-node-running-border)
  );
}
._node_15pfo_123._waiting_15pfo_222 {
  --canvas-node--border-color: var(
  	--color-canvas-node-waiting-border-color,
  	var(--color-secondary)
  );
}
._description_15pfo_159 {
  top: 100%;
  position: absolute;
  width: 100%;
  min-width: calc(var(--canvas-node--width) * 2);
  margin-top: var(--spacing-2xs);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4xs);
  pointer-events: none;
}
._label_15pfo_172,
._disabledLabel_15pfo_242 {
  font-size: var(--font-size-m);
  text-align: center;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  overflow-wrap: anywhere;
  font-weight: var(--font-weight-medium);
  line-height: var(--font-line-height-compact);
}
._subtitle_15pfo_175 {
  width: 100%;
  text-align: center;
  color: var(--color-text-light);
  font-size: var(--font-size-xs);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: var(--font-line-height-compact);
  font-weight: var(--font-weight-regular);
}
._statusIcons_15pfo_153 {
  position: absolute;
  bottom: var(--canvas-node--status-icons-offset);
  right: var(--canvas-node--status-icons-offset);
}
._icon_15pfo_156 {
  flex-grow: 0;
  flex-shrink: 0;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._sticky_1jyj2_123 {
  position: relative;
  /**
   * State classes
   * The reverse order defines the priority in case multiple states are active
   */
}
._sticky_1jyj2_123._selected_1jyj2_130 {
  box-shadow: 0 0 0 4px var(--color-canvas-selected);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._addNodes_164y0_123 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100px;
  height: 100px;
}
._addNodes_164y0_123:hover ._button_164y0_131 svg path {
  fill: var(--color-primary);
}
._button_164y0_131 {
  background: var(--color-foreground-xlight);
  border: 2px dashed var(--color-foreground-xdark);
  border-radius: 8px;
  padding: 0;
  min-width: 100px;
  min-height: 100px;
  cursor: pointer;
}
._label_164y0_145 {
  width: max-content;
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-m);
  line-height: var(--font-line-height-xloose);
  color: var(--color-text-dark);
  margin-top: var(--spacing-2xs);
  display: flex;
  flex-direction: column;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._container_145fr_123 {
  display: flex;
  flex-direction: row;
}
._promptContainer_145fr_128 {
  --width: 620px;
  --height: 150px;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2xs);
  width: var(--width);
  height: var(--height);
  padding: 0;
  border: 1px solid var(--color-foreground-dark);
  background-color: var(--color-background-xlight);
  border-radius: var(--border-radius-base);
  overflow: hidden;
}
._promptContainer_145fr_128._focused_145fr_142 {
  border: 1px solid var(--color-primary);
}
._form_145fr_146 {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
}
._form_textarea_145fr_153 {
  display: flex;
  flex: 1;
  min-height: 0;
  overflow: hidden;
  border: 0;
}
._form_textarea_145fr_153 .el-textarea__inner {
  height: 100%;
  min-height: 0;
  overflow-y: auto;
  border: 0;
  background: transparent;
  resize: none;
  font-family: var(--font-family);
}
._form_footer_145fr_170 {
  display: flex;
  justify-content: flex-end;
  padding: var(--spacing-2xs);
}
._or_145fr_176 {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 100px;
  cursor: auto;
}
._or_text_145fr_185 {
  font-size: var(--font-size-m);
  color: var(--color-text-base);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._rectangle_rd2w6_123 {
  width: var(--handle--indicator--width);
  height: var(--handle--indicator--height);
  background: var(--color-foreground-xdark);
}
._rectangle_rd2w6_123:hover {
  background: var(--color-primary);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._handle_1v80z_123 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
._label_1v80z_130 {
  position: absolute;
  top: 50%;
  left: calc(var(--spacing-xs) * -1);
  transform: translate(-100%, -50%);
  font-size: var(--font-size-2xs);
  color: var(--color-foreground-xdark);
  background: var(--color-canvas-label-background);
  z-index: 1;
  text-align: center;
  white-space: nowrap;
}
._required_1v80z_143 ._label_1v80z_130::after {
  content: "*";
  color: var(--color-danger);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._wrapper_hyidj_123 {
  position: relative;
}
._wrapper_hyidj_123._secondary_hyidj_126 ._line_hyidj_126 {
  stroke: var(--node-type-supplemental-color);
}
._wrapper_hyidj_123._secondary_hyidj_126 ._plus_hyidj_129 path {
  fill: var(--node-type-supplemental-color);
}
._wrapper_hyidj_123._secondary_hyidj_126 ._plus_hyidj_129 rect {
  stroke: var(--node-type-supplemental-color);
}
._wrapper_hyidj_123._success_hyidj_135 ._line_hyidj_126 {
  stroke: var(--color-success);
}
._wrapper_hyidj_123 ._plus_hyidj_129:hover {
  cursor: pointer;
}
._wrapper_hyidj_123 ._plus_hyidj_129:hover path {
  fill: var(--color-primary);
}
._wrapper_hyidj_123 ._plus_hyidj_129:hover rect {
  stroke: var(--color-primary);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._dot_gdjps_123 {
  width: var(--handle--indicator--width);
  height: var(--handle--indicator--height);
  border-radius: 50%;
  background: var(--color-foreground-xdark);
}
._dot_gdjps_123:hover {
  background: var(--color-primary);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._handle_1u904_123 {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
._handle_1u904_123._connected_1u904_129 ._label_1u904_129 {
  max-width: 96px;
}
._label_1u904_129 {
  position: absolute;
  background: var(--color-canvas-label-background);
  z-index: 1;
  max-width: calc(100% - var(--spacing-m) - 24px);
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
._required_1u904_143 ._label_1u904_129::after {
  content: "*";
  color: var(--color-danger);
}
._outputLabel_1u904_148 {
  top: 50%;
  left: var(--spacing-m);
  transform: translate(0, -50%);
  font-size: var(--font-size-2xs);
  color: var(--color-foreground-xdark);
}
._runDataLabel_1u904_156 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -150%);
  font-size: var(--font-size-xs);
  color: var(--color-text-base);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
.canvas-node-handle-main-output-enter-active,
.canvas-node-handle-main-output-leave-active {
  transform-origin: 0 center;
  transition-property: transform, opacity;
  transition-duration: 0.2s;
  transition-timing-function: ease;
}
.canvas-node-handle-main-output-enter-from,
.canvas-node-handle-main-output-leave-to {
  transform: scale(0);
  opacity: 0;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._diamond_kuwdo_123 {
  width: var(--handle--indicator--width);
  height: var(--handle--indicator--height);
  background: var(--node-type-supplemental-color);
  transform: rotate(45deg) scale(0.8);
}
._diamond_kuwdo_123:hover {
  background: var(--color-primary);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._handle_3v1rt_123 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
._label_3v1rt_130 {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translate(-50%, 0);
  font-size: var(--font-size-2xs);
  color: var(--node-type-supplemental-color);
  background: var(--color-canvas-label-background);
  z-index: 1;
  text-align: center;
  white-space: nowrap;
}
._required_3v1rt_143 ._label_3v1rt_130::after {
  content: "*";
  color: var(--color-danger);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
.canvas-node-handle-non-main-input-enter-active,
.canvas-node-handle-non-main-input-leave-active {
  transform-origin: center 0;
  transition-property: transform, opacity;
  transition-duration: 0.2s;
  transition-timing-function: ease;
}
.canvas-node-handle-non-main-input-enter-from,
.canvas-node-handle-non-main-input-leave-to {
  transform: scale(0);
  opacity: 0;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._handle_1x1ua_123 {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
._label_1x1ua_130 {
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translate(-50%, 0);
  font-size: var(--font-size-2xs);
  color: var(--node-type-supplemental-color);
  background: var(--color-canvas-label-background);
  z-index: 0;
  white-space: nowrap;
}
._required_1x1ua_142 ._label_1x1ua_130::after {
  content: "*";
  color: var(--color-danger);
}
.vue-flow__handle:not(.connectionindicator) ._plus_1x1ua_147 {
  display: none;
  position: absolute;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._handle_5ceyz_123 {
  --handle--indicator--width: 16px;
  --handle--indicator--height: 16px;
  width: var(--handle--indicator--width);
  height: var(--handle--indicator--height);
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border: 0;
  z-index: 1;
  background: transparent;
  border-radius: 0;
}
._handle_5ceyz_123._inputs_5ceyz_136._main_5ceyz_136 {
  --handle--indicator--width: 8px;
}
._renderType_5ceyz_140._top_5ceyz_140 {
  margin-bottom: calc(-1 * var(--handle--indicator--height));
  transform: translate(0%, -50%);
}
._renderType_5ceyz_140._right_5ceyz_144 {
  margin-left: calc(-1 * var(--handle--indicator--width));
  transform: translate(50%, 0%);
}
._renderType_5ceyz_140._left_5ceyz_148 {
  margin-right: calc(-1 * var(--handle--indicator--width));
  transform: translate(-50%, 0%);
}
._renderType_5ceyz_140._bottom_5ceyz_152 {
  margin-top: calc(-1 * var(--handle--indicator--height));
  transform: translate(0%, 50%);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._container_ze6b9_123 {
  z-index: -1;
  position: absolute;
  display: flex;
  align-items: center;
  height: 100%;
  right: 100%;
  top: 0;
  pointer-events: none;
}
._container_ze6b9_123 > div {
  position: relative;
  display: flex;
  align-items: center;
}
._container_ze6b9_123 button {
  margin-right: var(--spacing-s);
  opacity: 0;
  translate: -12px 0;
  transition: translate 0.1s ease-in, opacity 0.1s ease-in;
}
._container_ze6b9_123._interactive_ze6b9_144._hovered_ze6b9_144 button {
  opacity: 1;
  translate: 0 0;
  pointer-events: all;
}
._bolt_ze6b9_150 {
  position: absolute;
  right: 0;
  color: var(--color-primary);
  padding: var(--spacing-s);
  opacity: 1;
  translate: 0 0;
  transition: translate 0.1s ease-in, opacity 0.1s ease-in;
}
._container_ze6b9_123._interactive_ze6b9_144._hovered_ze6b9_144 ._bolt_ze6b9_150 {
  translate: -12px 0;
  opacity: 0;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._canvasNode_1ftik_123:hover:not(:has(> ._trigger_1ftik_123:hover)) ._canvasNodeToolbar_1ftik_123, ._canvasNode_1ftik_123:focus-within ._canvasNodeToolbar_1ftik_123, ._canvasNode_1ftik_123._showToolbar_1ftik_123 ._canvasNodeToolbar_1ftik_123 {
  opacity: 1;
}
._canvasNodeToolbar_1ftik_123 {
  transition: opacity 0.1s ease-in;
  position: absolute;
  top: 0;
  left: 50%;
  transform: translate(-50%, -100%);
  opacity: 0;
  z-index: 1;
}
._canvasNodeToolbar_1ftik_123:focus-within, ._canvasNodeToolbar_1ftik_123:hover {
  opacity: 1;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._canvas_1fjji_123 {
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 300ms ease;
}
._canvas_1fjji_123._ready_1fjji_129 {
  opacity: 1;
}
._canvas_1fjji_123 .vue-flow__pane {
  cursor: grab;
}
._canvas_1fjji_123 .vue-flow__pane.selection {
  cursor: default;
}
._canvas_1fjji_123 .vue-flow__pane.dragging {
  cursor: grabbing;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
.minimap-enter-active[data-v-5ad26715],
.minimap-leave-active[data-v-5ad26715] {
  transition: opacity 0.3s ease;
}
.minimap-enter-from[data-v-5ad26715],
.minimap-leave-to[data-v-5ad26715] {
  opacity: 0;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._component_wojby_123 {
  position: absolute;
  right: 0;
  z-index: 10;
  flex-grow: 0;
  flex-shrink: 0;
  border-left: var(--border-base);
  background-color: var(--color-background-xlight);
  width: 385px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s ease;
}
._component_wojby_123._closed_wojby_138 {
  transform: translateX(100%);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._wrapper_jyurh_123 {
  display: flex;
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
._canvas_jyurh_131 {
  width: 100%;
  height: 100%;
  position: relative;
  display: block;
  align-items: stretch;
  justify-content: stretch;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._wrapper_1ebmp_123 {
  display: flex;
  flex-direction: row nowrap;
  border-left: 1px solid var(--color-foreground-base);
  background: var(--color-background-xlight);
  overflow-y: hidden;
  height: 100%;
}
._container_1ebmp_132 {
  display: flex;
  flex-direction: column;
  height: 100%;
}
._content_1ebmp_138 {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}
._content_1ebmp_138._emptyContent_1ebmp_144 {
  text-align: center;
  justify-content: center;
  align-items: center;
}
._content_1ebmp_138._emptyContent_1ebmp_144 ._emptyText_1ebmp_149 {
  margin: 0 var(--spacing-xl);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2xs);
}
._content_1ebmp_138._emptyContent_1ebmp_144 ._emptyText_1ebmp_149 ._focusParameterWrapper_1ebmp_155 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2xs);
  margin-bottom: var(--spacing-m);
}
._content_1ebmp_138._emptyContent_1ebmp_144 ._emptyText_1ebmp_149 ._focusParameterWrapper_1ebmp_155 ._iconWrapper_1ebmp_162 {
  position: relative;
  display: inline-block;
}
._content_1ebmp_138._emptyContent_1ebmp_144 ._emptyText_1ebmp_149 ._focusParameterWrapper_1ebmp_155 ._pointerIcon_1ebmp_166 {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translate(-20%, -30%);
  pointer-events: none;
}
._content_1ebmp_138._emptyContent_1ebmp_144 ._emptyText_1ebmp_149 ._focusParameterWrapper_1ebmp_155 [class*="_disabled_"] {
  cursor: default !important;
}
._content_1ebmp_138 ._tabHeader_1ebmp_176 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--color-foreground-base);
  padding: var(--spacing-2xs);
}
._content_1ebmp_138 ._tabHeader_1ebmp_176 ._tabHeaderText_1ebmp_183 {
  display: flex;
  gap: var(--spacing-4xs);
  align-items: baseline;
}
._content_1ebmp_138 ._tabHeader_1ebmp_176 ._buttonWrapper_1ebmp_188 {
  display: flex;
  gap: var(--spacing-2xs);
  align-items: center;
}
._content_1ebmp_138 ._parameterDetailsWrapper_1ebmp_193 {
  display: flex;
  height: 100%;
  flex-direction: column;
  gap: var(--spacing-2xs);
  padding: var(--spacing-2xs);
}
._content_1ebmp_138 ._parameterDetailsWrapper_1ebmp_193 ._parameterOptionsWrapper_1ebmp_200 {
  display: flex;
  justify-content: space-between;
}
._content_1ebmp_138 ._parameterDetailsWrapper_1ebmp_193 ._noExecutionDataTip_1ebmp_204 {
  align-content: center;
}
._content_1ebmp_138 ._parameterDetailsWrapper_1ebmp_193 ._editorContainer_1ebmp_207 {
  height: 100%;
  overflow-y: auto;
}
._content_1ebmp_138 ._parameterDetailsWrapper_1ebmp_193 ._editorContainer_1ebmp_207 ._editor_1ebmp_207 {
  display: flex;
  height: 100%;
  width: 100%;
  font-size: var(--font-size-2xs);
}
._content_1ebmp_138 ._parameterDetailsWrapper_1ebmp_193 ._editorContainer_1ebmp_207 ._editor_1ebmp_207 .cm-editor {
  background-color: var(--color-code-background);
  width: 100%;
}
._delayedShow_1ebmp_222 {
  opacity: 0;
  transition: opacity 0.1s none;
  animation: _triggerShow_1ebmp_1 0.1s normal 0.1s forwards;
}
@keyframes _triggerShow_1ebmp_1 {
to {
    opacity: 1;
}
}
._closeButton_1ebmp_233 {
  cursor: pointer;
}
._heightFull_1ebmp_237 {
  height: 100%;
}
._forceHover_1ebmp_241 {
  color: var(--color-button-secondary-hover-active-focus-font);
  border-color: var(--color-button-secondary-hover-active-focus-border);
  background-color: var(--color-button-secondary-hover-active-focus-background);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._component_p52lz_123 {
  position: relative;
  display: flex;
  align-items: stretch;
}
._split_p52lz_129 ._button_p52lz_129 {
  height: var(--spacing-2xl);
  padding-inline-start: var(--spacing-xs);
  padding-block: 0;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
._divider_p52lz_137 {
  width: 1px;
  background-color: var(--button-font-color, var(--color-button-primary-font));
}
._chevron_p52lz_142 {
  width: 40px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
._menu_p52lz_148 .el-dropdown {
  height: 100%;
}
._menuItem_p52lz_152 {
  display: flex;
  align-items: center;
  gap: var(--spacing-2xs);
}
._menuItem_p52lz_152._disabled_p52lz_158 ._menuIcon_p52lz_158 {
  opacity: 0.2;
}
._buttonContent_p52lz_162 {
  display: flex;
  flex-direction: column;
  align-items: flex-start !important;
  gap: var(--spacing-5xs);
}
._subText_p52lz_169 {
  font-size: var(--font-size-2xs);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._wrapper_1xmhy_123 {
  display: flex;
  width: 100%;
}
._executionButtons_1xmhy_128 {
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  left: 50%;
  transform: translateX(-50%);
  bottom: var(--spacing-s);
  width: auto;
}
@media screen and (max-width: 991px) {
._executionButtons_1xmhy_128 {
    left: auto;
    right: var(--spacing-s);
    transform: none;
}
}
._executionButtons_1xmhy_128 button {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 0.625rem;
}
._executionButtons_1xmhy_128 button:first-child {
  margin: 0;
}
@media screen and (max-width: 767px) {
._executionButtons_1xmhy_128 button {
    text-indent: -10000px;
    width: 42px;
    height: 42px;
    padding: 0;
}
._executionButtons_1xmhy_128 button span {
    margin: 0;
}
}
._executionButtons_1xmhy_128 ._chatButton_1xmhy_165 {
  align-self: stretch;
}
._setupCredentialsButtonWrapper_1xmhy_169 {
  position: absolute;
  left: var(--spacing-s);
  top: var(--spacing-s);
}
._readOnlyEnvironmentNotification_1xmhy_175 {
  position: absolute;
  bottom: 16px;
  left: 50%;
  transform: translateX(-50%);
}