/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._wrapper_gfp08_123 {
  display: flex;
  justify-content: space-between;
}
._notFound_gfp08_128 {
  padding-top: var(--spacing-xl);
}
._title_gfp08_132 {
  width: 75%;
}
._button_gfp08_136 {
  display: block;
}
._image_gfp08_140 {
  width: 100%;
  height: 500px;
  border: var(--border-base);
  border-radius: var(--border-radius-large);
  overflow: hidden;
}
._image_gfp08_140 img {
  width: 100%;
}
._content_gfp08_151 {
  padding: var(--spacing-2xl) 0;
  display: flex;
  justify-content: space-between;
}
@media (max-width: 768px) {
._content_gfp08_151 {
    display: block;
}
}
._markdown_gfp08_162 {
  width: calc(100% - 180px);
  padding-right: var(--spacing-2xl);
  margin-bottom: var(--spacing-l);
}
@media (max-width: 768px) {
._markdown_gfp08_162 {
    width: 100%;
}
}
._details_gfp08_173 {
  width: 180px;
}