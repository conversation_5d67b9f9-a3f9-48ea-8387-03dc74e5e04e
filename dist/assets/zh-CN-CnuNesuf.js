const _reusableBaseText = { "cancel": "取消", "codeNodeEditor": { "linter": { "useJson": "访问 `.json` 下的项目属性，例如 `item.json`" }, "completer": { "all": "返回节点输出项的数组", "first": "返回节点输出的第一个项目", "last": "返回节点输出的最后一个项目", "itemMatching": "返回匹配的项目，即用于在指定索引处生成当前节点中项目的项目。" } }, "name": "名称", "save": "保存", "dismiss": "忽略", "unlimited": "无限", "activate": "激活", "error": "错误", "user": "用户", "enabled": "已启用", "disabled": "已禁用" };
const _reusableDynamicText = { "readMore": "阅读更多", "learnMore": "了解更多", "moreInfo": "更多信息", "oauth2": { "clientId": "客户端 ID", "clientSecret": "客户端密钥" } };
const error = "错误";
const forgotPassword = "忘记密码";
const settings = "设置";
const startupError = "连接到 n8n 时出错";
const zhCN = {
  _reusableBaseText,
  _reusableDynamicText,
  "generic.annotations": "注解",
  "generic.annotationData": "高亮显示的数据",
  "generic.any": "任何",
  "generic.cancel": "取消",
  "generic.open": "开放",
  "generic.close": "关闭",
  "generic.confirm": "确认",
  "generic.create": "创建",
  "generic.create.workflow": "创建工作流程",
  "generic.deleteWorkflowError": "删除工作流时出现问题",
  "generic.archiveWorkflowError": "问题归档工作流程",
  "generic.unarchiveWorkflowError": "问题解压工作流程",
  "generic.filtersApplied": "当前已应用筛选器。",
  "generic.field": "字段",
  "generic.fields": "字段",
  "generic.folderCount": "文件夹 | {count} 个文件夹 | {count} 个文件夹",
  "generic.folder": "文件夹",
  "generic.learnMore": "了解更多",
  "generic.reset": "重置",
  "generic.resetAllFilters": "重置所有筛选器",
  "generic.communityNode": "社区节点",
  "generic.communityNode.tooltip": '这是一个来自我们社区的节点。它是 {packageName} 包的一部分。 <a href="{docURL}" target="_blank" title="阅读 n8n 文档">了解更多</a>',
  "generic.officialNode.tooltip": "这是一个由{author}维护的官方节点",
  "generic.copy": "复制",
  "generic.delete": "删除",
  "generic.dontShowAgain": "不再显示",
  "generic.enterprise": "企业版",
  "generic.executions": "执行",
  "generic.tag_plural": "标签",
  "generic.tag": "标签 | {count} 个标签",
  "generic.tests": "评估",
  "generic.optional": "可选的",
  "generic.or": "或",
  "generic.clickToCopy": "点击复制",
  "generic.copiedToClipboard": "已复制到剪贴板",
  "generic.beta": "测试版",
  "generic.yes": "是",
  "generic.no": "否",
  "generic.rating": "评分",
  "generic.refresh": "刷新",
  "generic.retry": "重试",
  "generic.error": "发生了一些错误",
  "generic.settings": "设置",
  "generic.service": "服务",
  "generic.star": "星星",
  "generic.tryNow": "立即尝试",
  "generic.dismiss": "忽略",
  "generic.unsavedWork.confirmMessage.headline": "离开前保存更改？",
  "generic.unsavedWork.confirmMessage.message": "如果不保存，将会丢失更改。",
  "generic.unsavedWork.confirmMessage.confirmButtonText": "保存",
  "generic.unsavedWork.confirmMessage.cancelButtonText": "不保存就离开",
  "generic.upgrade": "升级",
  "generic.upgradeNow": "立即升级",
  "generic.credential": "凭证 | {count} 个凭证 | {count} 个凭证",
  "generic.credentials": "凭据",
  "generic.workflow": "工作流 | {count} 个工作流 | {count} 个工作流",
  "generic.workflowSaved": "工作流更改已保存",
  "generic.editor": "编辑器",
  "generic.seePlans": "查看计划",
  "generic.loading": "正在加载",
  "generic.and": "和",
  "generic.ownedByMe": "（您）",
  "generic.moreInfo": "更多信息",
  "generic.next": "下一步",
  "generic.pro": "专业版",
  "generic.variable_plural": "变量",
  "generic.folders_plural": "文件夹",
  "generic.variable": "变量 | {count} 个变量",
  "generic.viewDocs": "查看文档",
  "generic.workflows": "工作流程",
  "generic.rename": "重命名",
  "generic.missing.permissions": "执行此操作缺少权限",
  "generic.shortcutHint": "或按下",
  "generic.upgradeToEnterprise": "升级到企业版",
  "generic.never": "绝不",
  "about.aboutN8n": "关于 n8n",
  "about.close": "关闭",
  "about.license": "许可证",
  "about.n8nLicense": "可持续使用许可证 + n8n 企业许可证",
  "about.n8nVersion": "n8n 版本",
  "about.sourceCode": "源代码",
  "about.instanceID": "实例 ID",
  "about.debug.title": "调试",
  "about.debug.message": "复制调试信息",
  "about.debug.toast.title": "调试信息",
  "about.debug.toast.message": "已将调试信息复制到剪贴板",
  "askAi.dialog.title": "“询问 AI”即将准备就绪",
  "askAi.dialog.body": "我们仍在进行最后的润色。很快，您将能够<strong>从简单的文本提示自动生成代码</strong>。加入候补名单，提前获取此功能。",
  "askAi.dialog.signup": "加入候补名单",
  "activationModal.butYouCanSeeThem": "但您可以在",
  "activationModal.executionList": "执行列表",
  "activationModal.gotIt": "知道了",
  "activationModal.ifYouChooseTo": "如果您选择",
  "activationModal.saveExecutions": "保存执行。",
  "activationModal.theseExecutionsWillNotShowUp": "这些执行不会立即显示在编辑器中，",
  "activationModal.workflowActivated": "工作流已激活",
  "activationModal.yourTriggerWillNowFire": "您的触发器现在将自动触发生产执行。",
  "activationModal.yourTriggersWillNowFire": "您的触发器现在将自动触发生产执行。",
  "activationModal.yourWorkflowWillNowListenForEvents": "您的工作流现在将监听来自 {serviceName} 的事件并触发执行。",
  "activationModal.yourWorkflowWillNowRegularlyCheck": "您的工作流现在将定期检查 {serviceName} 的事件并为其触发执行。",
  "annotationTagsManager.manageTags": "管理执行标签",
  "annotationTagsView.usage": "使用情况（所有工作流）",
  "annotationTagsView.inUse": "{count} 个执行 | {count} 个执行",
  "auth.changePassword": "更改密码",
  "auth.changePassword.currentPassword": "当前密码",
  "auth.changePassword.mfaCode": "双因素代码",
  "auth.changePassword.error": "更改密码时出现问题",
  "auth.changePassword.missingTokenError": "缺少令牌",
  "auth.changePassword.missingUserIdError": "缺少用户 ID",
  "auth.changePassword.passwordUpdated": "密码已更新",
  "auth.changePassword.passwordUpdatedMessage": "您现在可以使用新密码登录",
  "auth.changePassword.passwordsMustMatchError": "密码必须匹配",
  "auth.changePassword.reenterNewPassword": "重新输入新密码",
  "auth.changePassword.tokenValidationError": "无效的密码重置令牌",
  "auth.defaultPasswordRequirements": "8 个以上字符，至少 1 个数字和 1 个大写字母",
  "auth.validation.missingParameters": "缺少令牌或用户 ID",
  "auth.email": "电子邮件",
  "auth.firstName": "名字",
  "auth.lastName": "姓氏",
  "auth.newPassword": "新密码",
  "auth.password": "密码",
  "auth.role": "角色",
  "auth.roles.default": "默认",
  "auth.roles.member": "成员",
  "auth.roles.admin": "管理员",
  "auth.roles.owner": "所有者",
  "auth.agreement.label": "我希望接收安全和产品更新",
  "auth.setup.next": "下一步",
  "auth.setup.settingUpOwnerError": "设置所有者时出现问题",
  "auth.setup.setupOwner": "设置所有者帐户",
  "auth.signin": "登录",
  "auth.signin.error": "登录时出现问题",
  "auth.signout": "注销",
  "auth.signout.error": "无法注销",
  "auth.signup.finishAccountSetup": "完成帐户设置",
  "auth.signup.missingTokenError": "缺少令牌",
  "auth.signup.setupYourAccount": "设置您的帐户",
  "auth.signup.setupYourAccountError": "设置您的帐户时出现问题",
  "auth.signup.tokenValidationError": "验证邀请令牌时出现问题",
  "aiAssistant.name": "助手",
  "aiAssistant.n8nAi": "n8n AI",
  "aiAssistant.builder.name": "建设者",
  "aiAssistant.builder.mode": "AI构建器",
  "aiAssistant.builder.placeholder": "您想自动化什么？",
  "aiAssistant.builder.generateNew": "生成新工作流程",
  "aiAssistant.builder.buildWorkflow": "构建工作流程",
  "aiAssistant.builder.newWorkflowNotice": "创建的工作流将被添加到编辑器中",
  "aiAssistant.builder.feedbackPrompt": "这个工作流程有帮助吗？",
  "aiAssistant.builder.invalidPrompt": "提示验证失败。请重新尝试，更清晰地描述您的工作流程需求及支持的集成功能。",
  "aiAssistant.assistant": "AI助手",
  "aiAssistant.newSessionModal.title.part1": "开始新的",
  "aiAssistant.newSessionModal.title.part2": "会话",
  "aiAssistant.newSessionModal.message": "您已经有一个活动的 AI 助手会话。开始新的会话将清除您当前的对话历史记录。",
  "aiAssistant.newSessionModal.question": "您确定要开始新的会话吗？",
  "aiAssistant.newSessionModal.confirm": "开始新的会话",
  "aiAssistant.serviceError.message": "无法连接到 n8n 的 AI 服务",
  "aiAssistant.payloadTooBig.message": "负载数据大小超出限制",
  "aiAssistant.codeUpdated.message.title": "助手修改了工作流",
  "aiAssistant.codeUpdated.message.body1": "打开",
  "aiAssistant.codeUpdated.message.body2": "节点以查看更改",
  "aiAssistant.thinkingSteps.analyzingError": "正在分析错误...",
  "aiAssistant.thinkingSteps.thinking": "正在思考...",
  "aiAssistant.prompts.currentView.workflowList": "用户当前正在查看工作流列表。",
  "aiAssistant.prompts.currentView.credentialsList": "用户当前正在查看凭证列表。",
  "aiAssistant.prompts.currentView.executionsView": "用户当前正在查看当前打开的工作流的执行列表。",
  "aiAssistant.prompts.currentView.workflowEditor": "用户当前正在 n8n 编辑器中查看当前工作流，而未选择任何特定节点。",
  "aiAssistant.tooltip": "请求助手",
  "banners.confirmEmail.message.1": "为了确保您的帐户安全并防止将来出现访问问题，请确认您的",
  "banners.confirmEmail.message.2": "电子邮件地址。",
  "banners.confirmEmail.button": "确认电子邮件",
  "banners.confirmEmail.toast.success.heading": "已发送确认电子邮件",
  "banners.confirmEmail.toast.success.message": "请检查您的收件箱并点击确认链接。",
  "banners.confirmEmail.toast.error.heading": "发送确认电子邮件时出现问题",
  "banners.confirmEmail.toast.error.message": "请稍后重试。",
  "banners.nonProductionLicense.message": "此 n8n 实例未获得生产用途许可。",
  "banners.trial.message": "您的 n8n 试用期还剩 1 天 | 您的 n8n 试用期还剩 {count} 天",
  "banners.trialOver.message": "您的试用期已结束。立即升级以继续自动化。",
  "banners.v1.message": 'n8n 已更新至版本 1，引入了一些重大更改。有关更多信息，请参阅<a target="_blank" href="https://docs.n8n.io/1-0-migration-checklist">迁移指南</a>。',
  "binaryDataDisplay.backToList": "返回列表",
  "binaryDataDisplay.backToOverviewPage": "返回概览页",
  "binaryDataDisplay.noDataFoundToDisplay": "未找到要显示的数据",
  "binaryDataDisplay.yourBrowserDoesNotSupport": "您的浏览器不支持 video 元素。请将其更新到最新版本。",
  "chat.hide": "隐藏聊天",
  "chat.open": "打开聊天",
  "chat.window.title": "聊天",
  "chat.window.logs": "最新日志",
  "chat.window.logsFromNode": "来自 {nodeName} 节点",
  "chat.window.noChatNode": "无聊天节点",
  "chat.window.noExecution": "尚未执行任何操作",
  "chat.window.chat.placeholder": "键入消息，或按“向上”箭头查看上一条消息",
  "chat.window.chat.placeholderPristine": "键入消息",
  "chat.window.chat.sendButtonText": "发送",
  "chat.window.chat.provideMessage": "请提供消息",
  "chat.window.chat.emptyChatMessage": "空聊天消息",
  "chat.window.chat.emptyChatMessage.v2": "发送下方消息以触发聊天流程",
  "chat.window.chat.chatMessageOptions.reuseMessage": "重用消息",
  "chat.window.chat.chatMessageOptions.repostMessage": "重新发布消息",
  "chat.window.chat.chatMessageOptions.executionId": "执行 ID",
  "chat.window.chat.unpinAndExecute.description": "发送消息将覆盖固定的聊天节点数据。",
  "chat.window.chat.unpinAndExecute.title": "取消固定聊天输出数据？",
  "chat.window.chat.unpinAndExecute.confirm": "取消固定并发送",
  "chat.window.chat.unpinAndExecute.cancel": "取消",
  "chat.window.chat.response.empty": "[无响应。请确保最后执行的节点输出要在此处显示的内容]",
  "chat.window.session.title": "会话",
  "chat.window.session.id": "会话：{id}",
  "chat.window.session.id.copy": "（点击复制）",
  "chat.window.session.reset": "重置",
  "chat.window.session.resetSession": "重置聊天会话",
  "chatEmbed.infoTip.description": "使用 n8n 聊天包将聊天添加到外部应用程序。",
  "chatEmbed.infoTip.link": "更多信息",
  "chatEmbed.title": "将聊天嵌入到您的网站中",
  "chatEmbed.close": "关闭",
  "chatEmbed.install": "首先，安装 n8n 聊天包：",
  "chatEmbed.paste.cdn": "将以下代码粘贴到 HTML 文件的 {code} 标记中的任何位置。",
  "chatEmbed.paste.cdn.file": "<body>",
  "chatEmbed.paste.vue": "接下来，将以下代码粘贴到您的 {code} 文件中。",
  "chatEmbed.paste.vue.file": "App.vue",
  "chatEmbed.paste.react": "接下来，将以下代码粘贴到您的 {code} 文件中。",
  "chatEmbed.paste.react.file": "App.ts",
  "chatEmbed.paste.other": "接下来，将以下代码粘贴到您的 {code} 文件中。",
  "chatEmbed.paste.other.file": "main.ts",
  "chatEmbed.packageInfo.description": "n8n 聊天小部件可以轻松定制以满足您的需求。",
  "chatEmbed.packageInfo.link": "阅读完整文档",
  "chatEmbed.chatTriggerNode": "您可以使用聊天触发节点将聊天小部件直接嵌入到 n8n 中。",
  "chatEmbed.url": "https://www.npmjs.com/package/{'@'}n8n/chat",
  "codeEdit.edit": "编辑",
  "codeNodeEditor.askAi": "✨ 询问 AI",
  "codeNodeEditor.completer.$()": "{nodeName} 节点的输出数据",
  "codeNodeEditor.completer.$execution": "检索或设置当前执行的元数据",
  "codeNodeEditor.completer.$execution.id": "当前工作流执行的 ID",
  "codeNodeEditor.completer.$execution.mode": "返回 <code>test</code>（表示该执行是通过点击 n8n 中的按钮触发的）或 <code>production</code>（表示该执行是自动触发的）",
  "codeNodeEditor.completer.$execution.resumeUrl": "调用以恢复在“等待”节点处等待的工作流的 Webhook URL。",
  "codeNodeEditor.completer.$execution.resumeFormUrl": "用于访问“等待”节点生成的表单的 URL。",
  "codeNodeEditor.completer.$execution.customData": "设置和获取自定义执行数据（例如，用于按此筛选执行）。您还可以使用“执行数据”节点执行此操作。",
  "codeNodeEditor.completer.$execution.customData.set": "在指定的键下存储自定义执行数据。使用此选项可以轻松按此数据筛选执行。",
  "codeNodeEditor.completer.$execution.customData.set.args.key": "存储数据的键（标识符）",
  "codeNodeEditor.completer.$execution.customData.set.args.value": "要存储的数据",
  "codeNodeEditor.completer.$execution.customData.set.examples.1": "存储用户的电子邮件，以便稍后轻松检索与该用户相关的所有执行",
  "codeNodeEditor.completer.$execution.customData.get": "返回在给定键下存储的自定义执行数据。",
  "codeNodeEditor.completer.$execution.customData.get.args.key": "存储数据的键（标识符）",
  "codeNodeEditor.completer.$execution.customData.get.examples.1": "获取用户的电子邮件（先前已存储）",
  "codeNodeEditor.completer.$execution.customData.setAll": "为执行设置多个自定义数据的键值对。使用此选项可以轻松按此数据筛选执行。",
  "codeNodeEditor.completer.$execution.customData.setAll.args.obj": "包含要设置的数据的键值对的 JavaScript 对象",
  "codeNodeEditor.completer.$execution.customData.getAll": "返回当前执行中已设置的所有自定义数据的键值对。",
  "codeNodeEditor.completer.$ifEmpty": `如果第一个参数不为空，则返回第一个参数，否则返回第二个参数。以下内容视为空： <code>"</code>、<code>[]</code>、<code>{'{}'}</code>、<code>null</code>、<code>undefined</code>`,
  "codeNodeEditor.completer.$ifEmpty.args.value": "如果该值不为空，则返回该值",
  "codeNodeEditor.completer.$ifEmpty.args.valueIfEmpty": "如果 <code>value</code> 为空，则返回该值",
  "codeNodeEditor.completer.$input": "当前节点的输入数据",
  "codeNodeEditor.completer.$input.all": "@:_reusableBaseText.codeNodeEditor.completer.all",
  "codeNodeEditor.completer.$input.first": "@:_reusableBaseText.codeNodeEditor.completer.first",
  "codeNodeEditor.completer.$input.item": "生成当前项的项目",
  "codeNodeEditor.completer.$input.itemMatching": "@:_reusableBaseText.codeNodeEditor.completer.itemMatching",
  "codeNodeEditor.completer.$input.last": "@:_reusableBaseText.codeNodeEditor.completer.last",
  "codeNodeEditor.completer.$itemIndex": "当前正在处理的项在输入项列表中的位置",
  "codeNodeEditor.completer.$jmespath": '使用 <a target="_blank" href="https://docs.n8n.io/code/cookbook/jmespath/">JMESPath</a> 表达式从对象（或对象数组）中提取数据。对于查询复杂的嵌套对象非常有用。如果表达式无效，则返回 <code>undefined</code>。',
  "codeNodeEditor.completer.$jmespath.args.obj": "从中检索数据的对象或对象数组",
  "codeNodeEditor.completer.$jmespath.args.expression": '用于定义要从对象中检索的数据的 <a target="_blank" href="https://jmespath.org/examples.html">JMESPath 表达式</a>',
  "codeNodeEditor.completer.$jmespath.examples.1": "获取数组中的所有名称",
  "codeNodeEditor.completer.$jmespath.examples.2": "获取所有 20 岁以下的人的姓名和年龄",
  "codeNodeEditor.completer.$jmespath.examples.3": "获取 20 岁以下第一个人的姓名",
  "codeNodeEditor.completer.$jmespath.examples.4": "获取每个预订中需要双人间的客人的姓名",
  "codeNodeEditor.completer.$if": "根据 <code>condition</code> 返回两个值之一。类似于 JavaScript 中的 <code>?</code> 运算符。",
  "codeNodeEditor.completer.$if.args.condition": "要进行的检查。应计算为 <code>true</code> 或 <code>false</code>",
  "codeNodeEditor.completer.$if.args.valueIfTrue": "如果条件为 true，则返回该值",
  "codeNodeEditor.completer.$if.args.valueIfFalse": "如果条件为 false，则返回该值",
  "codeNodeEditor.completer.$if.examples.1": "如果时间在下午 5 点之前，则返回“早上好”，否则返回“晚上好”",
  "codeNodeEditor.completer.$if.examples.2": "可以组合 $if() 调用\n如果时间在上午 10 点之前，则返回“早上好”，如果时间在下午 5 点之前，则返回“早上好”，否则返回“晚上好”",
  "codeNodeEditor.completer.$max": "返回给定数字中的最大值，如果没有参数，则返回 -Infinity。",
  "codeNodeEditor.completer.$max.args.numbers": "要比较的数字",
  "codeNodeEditor.completer.$min": "返回给定数字中的最小值，如果没有参数，则返回 Infinity。",
  "codeNodeEditor.completer.$now": "表示当前时刻的 DateTime。\n\n使用工作流的时区（可以在工作流设置中更改）。",
  "codeNodeEditor.completer.$parameter": "当前节点的配置设置。这些是在节点的 UI 中填写的参数（例如，其操作）。",
  "codeNodeEditor.completer.$prevNode": "有关当前输入来源的节点的信息。\n\n在“合并”节点中，始终使用第一个输入连接器。",
  "codeNodeEditor.completer.$prevNode.name": "当前输入来源的节点的名称。\n\n如果存在多个（例如，在“合并”节点中），则始终使用当前节点的第一个输入连接器。",
  "codeNodeEditor.completer.$prevNode.outputIndex": "当前输入来源的输出连接器的索引。如果上一个节点有多个输出（例如，“If”或“Switch”节点），请使用此选项。\n\n如果存在多个（例如，在“合并”节点中），则始终使用当前节点的第一个输入连接器。",
  "codeNodeEditor.completer.$prevNode.runIndex": "生成当前输入的上一个节点的运行。\n\n如果存在多个（例如，在“合并”节点中），则始终使用当前节点的第一个输入连接器。",
  "codeNodeEditor.completer.$runIndex": "当前节点执行的当前运行的索引。从 0 开始。",
  "codeNodeEditor.completer.$nodeVersion": "当前节点的版本（显示在节点设置窗格的底部）",
  "codeNodeEditor.completer.$today": "表示当前一天开始时的午夜的 DateTime。\n\n使用实例的时区（除非在工作流的设置中覆盖）。",
  "codeNodeEditor.completer.$vars": '工作流可用的<a target="_blank" href="https://docs.n8n.io/code/variables/">变量</a>',
  "codeNodeEditor.completer.$vars.varName": "在此 n8n 实例上设置的变量。所有变量都计算为字符串。",
  "codeNodeEditor.completer.$secrets": '来自<a target="_blank" href="https://docs.n8n.io/external-secrets/">外部秘密保管库</a>的密钥（如果已配置）。密钥值永远不会显示给用户。仅在凭证字段中可用。',
  "codeNodeEditor.completer.$secrets.provider": "连接到此 n8n 实例的外部密钥提供商。",
  "codeNodeEditor.completer.$secrets.provider.varName": "连接到此 n8n 实例的外部密钥。所有密钥都计算为字符串。",
  "codeNodeEditor.completer.$workflow": "有关当前工作流的信息",
  "codeNodeEditor.completer.$workflow.active": "工作流是否处于活动状态",
  "codeNodeEditor.completer.$workflow.id": "工作流 ID。也可以在工作流的 URL 中找到。",
  "codeNodeEditor.completer.$workflow.name": "工作流的名称，如编辑器顶部所示",
  "codeNodeEditor.completer.$response": "上次 HTTP 调用返回的响应。仅在“HTTP 请求”节点中可用。",
  "codeNodeEditor.completer.$response.headers": "上次 HTTP 调用返回的标头。仅在“HTTP 请求”节点中可用。",
  "codeNodeEditor.completer.$response.statusCode": "上次 HTTP 调用返回的 HTTP 状态代码。仅在“HTTP 请求”节点中可用。",
  "codeNodeEditor.completer.$response.statusMessage": "关于请求状态的可选消息。仅在“HTTP 请求”节点中可用。",
  "codeNodeEditor.completer.$response.body": "上次 HTTP 调用返回的响应对象的主体。仅在“HTTP 请求”节点中可用",
  "codeNodeEditor.completer.$request": "在上次节点运行时发送的请求对象。仅在“HTTP 请求”节点中可用。",
  "codeNodeEditor.completer.$pageCount": "节点已提取的结果页面数。仅在“HTTP 请求”节点中可用。",
  "codeNodeEditor.completer.dateTime": "Luxon DateTime。使用此对象解析、格式化和操作日期和时间。",
  "codeNodeEditor.completer.binary": "返回当前节点当前项的任何二进制输入数据。<code>$input.item.binary</code> 的简写。",
  "codeNodeEditor.completer.binary.mimeType": "表示文件内容格式的字符串，例如 <code>image/jpeg</code>",
  "codeNodeEditor.completer.binary.fileSize": "表示文件大小的字符串（例如 <code>1 kB</code>）",
  "codeNodeEditor.completer.binary.fileName": "文件的名称，包括扩展名",
  "codeNodeEditor.completer.binary.fileExtension": "附加到文件名的后缀（例如，<code>txt</code>）",
  "codeNodeEditor.completer.binary.fileType": "表示文件类型的字符串，例如 <code>image</code>。对应于 MIME 类型的第一部分。",
  "codeNodeEditor.completer.binary.id": "文件的唯一 ID。用于标识存储在磁盘上或 S3 等存储服务中的文件。",
  "codeNodeEditor.completer.binary.directory": "存储文件所在目录的路径。用于区分不同目录中同名的文件。如果 n8n 配置为将文件存储在其数据库中，则不会设置。",
  "codeNodeEditor.completer.item.binary": "返回该项包含的任何二进制数据。",
  "codeNodeEditor.completer.item.json": "返回该项包含的 JSON 数据。",
  "codeNodeEditor.completer.math": "数学实用方法",
  "codeNodeEditor.completer.globalObject": "操作 JavaScript 对象的方法",
  "codeNodeEditor.completer.globalObject.assign": "将所有可枚举的对象属性合并到目标对象中。返回修改后的目标对象。",
  "codeNodeEditor.completer.globalObject.entries": "对象的键和值",
  "codeNodeEditor.completer.globalObject.keys": "对象的键",
  "codeNodeEditor.completer.globalObject.values": "对象的值",
  "codeNodeEditor.completer.json": "返回当前节点的当前项的 JSON 输入数据。<code>$input.item.json</code> 的简写。",
  "codeNodeEditor.completer.luxon.dateTimeStaticMethods.expandFormat": "生成区域设置的完全展开的格式令牌，不会引用字符，因此引用的令牌不会正确往返。",
  "codeNodeEditor.completer.luxon.dateTimeStaticMethods.fromFormat": "从输入字符串和格式字符串创建 DateTime。",
  "codeNodeEditor.completer.luxon.dateTimeStaticMethods.fromFormatExplain": "说明如何通过 fromFormat() 解析字符串。",
  "codeNodeEditor.completer.luxon.dateTimeStaticMethods.fromHTTP": "从 HTTP 标头日期创建 DateTime",
  "codeNodeEditor.completer.luxon.dateTimeStaticMethods.fromISO": "从 ISO 8601 字符串创建 DateTime",
  "codeNodeEditor.completer.luxon.dateTimeStaticMethods.fromISO.args.isoString": "要转换为 DateTime 的 ISO 8601 字符串",
  "codeNodeEditor.completer.luxon.dateTimeStaticMethods.fromISO.args.opts": '配置选项。有关更多信息，请参阅 <a target="blank" href="https://moment.github.io/luxon/api-docs/index.html#datetimefromiso">Luxon 文档</a>。',
  "codeNodeEditor.completer.luxon.dateTimeStaticMethods.fromJSDate": "从 JavaScript Date 对象创建 DateTime。使用默认时区",
  "codeNodeEditor.completer.luxon.dateTimeStaticMethods.fromMillis": "从自 epoch 以来（即自 1970 年 1 月 1 日 00:00:00 UTC 以来）的毫秒数创建 DateTime。使用默认时区",
  "codeNodeEditor.completer.luxon.dateTimeStaticMethods.fromMillis.args.milliseconds": "自 epoch 以来（即自 1970 年 1 月 1 日 00:00:00 UTC 以来）的毫秒数",
  "codeNodeEditor.completer.luxon.dateTimeStaticMethods.fromMillis.args.opts": '配置选项。有关更多信息，请参阅 <a target="blank" href="https://moment.github.io/luxon/api-docs/index.html#datetimefrommillis">Luxon 文档</a>。',
  "codeNodeEditor.completer.luxon.dateTimeStaticMethods.fromObject": "从 JavaScript 对象创建 DateTime，该对象具有诸如“年”和“小时”之类的键，以及合理的默认值",
  "codeNodeEditor.completer.luxon.dateTimeStaticMethods.fromRFC2822": "从 RFC 2822 字符串创建 DateTime",
  "codeNodeEditor.completer.luxon.dateTimeStaticMethods.fromString": "已弃用：请改用 `fromFormat`。",
  "codeNodeEditor.completer.luxon.dateTimeStaticMethods.fromStringExplain": "已弃用：请改用 `fromFormatExplain`。",
  "codeNodeEditor.completer.luxon.dateTimeStaticMethods.fromSQL": "从 SQL 日期、时间或日期时间创建 DateTime",
  "codeNodeEditor.completer.luxon.dateTimeStaticMethods.fromSeconds": "从自 epoch 以来（即自 1970 年 1 月 1 日 00:00:00 UTC 以来）的秒数创建 DateTime。使用默认时区",
  "codeNodeEditor.completer.luxon.dateTimeStaticMethods.fromSeconds.args.seconds": "自 epoch 以来（即自 1970 年 1 月 1 日 00:00:00 UTC 以来）的秒数",
  "codeNodeEditor.completer.luxon.dateTimeStaticMethods.fromSeconds.args.opts": '配置选项。有关更多信息，请参阅 <a target="blank" href="https://moment.github.io/luxon/api-docs/index.html#datetimefromseconds">Luxon 文档</a>。',
  "codeNodeEditor.completer.luxon.dateTimeStaticMethods.invalid": "创建无效的 DateTime。",
  "codeNodeEditor.completer.luxon.dateTimeStaticMethods.isDateTime": "检查对象是否为 DateTime。跨上下文边界工作",
  "codeNodeEditor.completer.luxon.dateTimeStaticMethods.isDateTime.args.maybeDateTime": "要检查的潜在 DateTime。只有 Luxon DateTime 类的实例才会返回 <code>true</code>。",
  "codeNodeEditor.completer.luxon.dateTimeStaticMethods.local": "创建本地 DateTime",
  "codeNodeEditor.completer.luxon.dateTimeStaticMethods.max": "返回多个日期时间的最大值。",
  "codeNodeEditor.completer.luxon.dateTimeStaticMethods.max.args.dateTimes": "要比较的 DateTime 对象",
  "codeNodeEditor.completer.luxon.dateTimeStaticMethods.min": "返回多个日期时间的最小值。",
  "codeNodeEditor.completer.luxon.dateTimeStaticMethods.min.args.dateTimes": "要比较的 DateTime 对象",
  "codeNodeEditor.completer.luxon.dateTimeStaticMethods.now": "在工作流的本地时区为当前时刻创建 DateTime",
  "codeNodeEditor.completer.luxon.dateTimeStaticMethods.parseFormatForOpts": "生成区域设置的完全展开的格式令牌，不会引用字符，因此引用的令牌不会正确往返。",
  "codeNodeEditor.completer.luxon.dateTimeStaticMethods.utc": "在 UTC 中创建 DateTime",
  "codeNodeEditor.completer.luxon.instanceMethods.day": "月份中的日期 (1-31)。",
  "codeNodeEditor.completer.luxon.instanceMethods.daysInMonth": "返回此 DateTime 所在月份的天数。",
  "codeNodeEditor.completer.luxon.instanceMethods.daysInYear": "返回此 DateTime 所在年份的天数。",
  "codeNodeEditor.completer.luxon.instanceMethods.diff": "将两个 DateTime 之间的差异作为 Duration 返回。",
  "codeNodeEditor.completer.luxon.instanceMethods.diffNow": "返回此 DateTime 与当前时间之间的差异。",
  "codeNodeEditor.completer.luxon.instanceMethods.endOf": "将 DateTime 四舍五入到其一个单位的末尾，例如月份的末尾",
  "codeNodeEditor.completer.luxon.instanceMethods.endOf.args.unit": "要四舍五入到的末尾的单位。可以是 <code>year</code>、<code>quarter</code>、<code>month</code>、<code>week</code>、<code>day</code>、<code>hour</code>、<code>minute</code>、<code>second</code> 或 <code>millisecond</code>。",
  "codeNodeEditor.completer.luxon.instanceMethods.endOf.args.opts": "包含影响输出的选项的对象。可能的属性：\n<code>useLocaleWeeks</code> (boolean)：是否在计算周的开始时使用区域设置。默认为 false。",
  "codeNodeEditor.completer.luxon.instanceMethods.equals": "如果两个 DateTime 表示完全相同的时刻并且在同一时区，则返回 <code>true</code>。对于不太严格的比较，请使用 <code>hasSame()</code>。",
  "codeNodeEditor.completer.luxon.instanceMethods.equals.args.other": "要比较的另一个 DateTime",
  "codeNodeEditor.completer.luxon.instanceMethods.hasSame": "如果两个 DateTime 在指定的单位内相同，则返回 true。时区将被忽略（仅比较本地时间），因此如果需要，请先使用 toUTC()。",
  "codeNodeEditor.completer.luxon.instanceMethods.hasSame.args.other": "要比较的另一个 DateTime",
  "codeNodeEditor.completer.luxon.instanceMethods.hasSame.args.unit": "要检查相同性直至的时间单位。<code>year</code>、<code>quarter</code>、<code>month</code>、<code>week</code>、<code>day</code>、<code>hour</code>、<code>minute</code>、<code>second</code> 或 <code>millisecond</code> 之一。",
  "codeNodeEditor.completer.luxon.instanceMethods.hour": "一天中的小时 (0-23)。",
  "codeNodeEditor.completer.luxon.instanceMethods.invalidExplanation": "返回此 DateTime 变为无效的原因的说明，如果 DateTime 有效，则返回 null。",
  "codeNodeEditor.completer.luxon.instanceMethods.invalidReason": "如果此 DateTime 无效，则返回错误代码，如果 DateTime 有效，则返回 null。",
  "codeNodeEditor.completer.luxon.instanceMethods.isInDST": "DateTime 是否处于夏令时。",
  "codeNodeEditor.completer.luxon.instanceMethods.isInLeapYear": "DateTime 是否处于闰年。",
  "codeNodeEditor.completer.luxon.instanceMethods.isOffsetFixed": "获取此时区的偏移量是否会发生变化，如在夏令时中。",
  "codeNodeEditor.completer.luxon.instanceMethods.isValid": "返回 DateTime 是否有效。当 DateTime 是从无效的日历信息创建时（例如，第 13 个月或 2 月 30 日）时，将发生无效的 DateTime。DateTime 是通过对另一个无效日期执行操作创建的。",
  "codeNodeEditor.completer.luxon.instanceMethods.locale": "DateTime 的区域设置，例如“en-GB”。格式化 DateTime 时使用该区域设置。",
  "codeNodeEditor.completer.luxon.instanceMethods.max": "返回多个日期时间的最大值。",
  "codeNodeEditor.completer.luxon.instanceMethods.millisecond": "秒的毫秒 (0-999)。",
  "codeNodeEditor.completer.luxon.instanceMethods.min": "返回多个日期时间的最小值",
  "codeNodeEditor.completer.luxon.instanceMethods.minus": "减去小时、分钟、秒或毫秒会使时间戳增加正确的毫秒数。",
  "codeNodeEditor.completer.luxon.instanceMethods.minute": "小时的分钟数 (0-59)。",
  "codeNodeEditor.completer.luxon.instanceMethods.month": "月份 (1-12)。",
  "codeNodeEditor.completer.luxon.instanceMethods.monthLong": "文本长月份名称，例如“October”。如果未指定，则默认为系统的区域设置。",
  "codeNodeEditor.completer.luxon.instanceMethods.monthShort": "文本缩写月份名称，例如“Oct”。如果未指定，则默认为系统的区域设置。",
  "codeNodeEditor.completer.luxon.instanceMethods.numberingSystem": "获取 DateTime 的编号系统，例如“beng”。格式化 DateTime 时使用该编号系统。",
  "codeNodeEditor.completer.luxon.instanceMethods.offset": "获取此 DateTime 的 UTC 偏移量（以分钟为单位）",
  "codeNodeEditor.completer.luxon.instanceMethods.offsetNameLong": "获取时区当前偏移量的长人名，例如“东部标准时间”或“东部夏令时”。",
  "codeNodeEditor.completer.luxon.instanceMethods.offsetNameShort": "获取时区当前偏移量的短人名，例如“EST”或“EDT”。'",
  "codeNodeEditor.completer.luxon.instanceMethods.offsetNumber": "获取时区当前偏移量的短人名，例如“EST”或“EDT”。'",
  "codeNodeEditor.completer.luxon.instanceMethods.ordinal": "获取序号（即一年中的日期）。",
  "codeNodeEditor.completer.luxon.instanceMethods.outputCalendar": "获取 DateTime 的输出日历，例如“伊斯兰”。格式化 DateTime 时使用输出日历。",
  "codeNodeEditor.completer.luxon.instanceMethods.plus": "增加小时、分钟、秒或毫秒会使时间戳增加正确的毫秒数。",
  "codeNodeEditor.completer.luxon.instanceMethods.quarter": "一年中的季度 (1-4)。",
  "codeNodeEditor.completer.luxon.instanceMethods.reconfigure": "“设置”区域设置、编号系统或输出日历。返回新构造的 DateTime。",
  "codeNodeEditor.completer.luxon.instanceMethods.resolvedLocaleOptions": "返回此 DateTime 的已解析 Intl 选项。这对于了解格式化方法的行为很有用。",
  "codeNodeEditor.completer.luxon.instanceMethods.second": "分钟的秒数 (0-59)。",
  "codeNodeEditor.completer.luxon.instanceMethods.set": "将新值分配给 DateTime 的指定单位。要对 DateTime 进行四舍五入，另请参阅 <code>startOf()</code> 和 <code>endOf()</code>。",
  "codeNodeEditor.completer.luxon.instanceMethods.set.args.values": "包含要设置的单位和要分配的相应值的对象。可能的键为 <code>year</code>、<code>month</code>、<code>day</code>、<code>hour</code>、<code>minute</code>、<code>second</code> 和 <code>millsecond</code>。",
  "codeNodeEditor.completer.luxon.instanceMethods.setLocale": "设置区域设置，该区域设置确定 DateTime 的语言和格式。在生成 DateTime 的文本表示形式时非常有用，例如，使用 <code>format()</code> 或 <code>toLocaleString()</code>。",
  "codeNodeEditor.completer.luxon.instanceMethods.setLocale.args.locale": '要设置的区域设置，例如，英式英语的“en-GB”或巴西葡萄牙语的“pt-BR”。<a target="blank" href=”https://www.localeplanet.com/icu/”>列表</a>（非官方）',
  "codeNodeEditor.completer.luxon.instanceMethods.setZone": "将 DateTime 转换为给定的时区。除非在选项中指定，否则 DateTime 仍表示相同的时刻。另请参阅 <code>toLocal()</code> 和 <code>toUTC()</code>。",
  "codeNodeEditor.completer.luxon.instanceMethods.setZone.args.zone": "区域标识符，格式为 <code>'America/New_York'</code>、<code>'UTC+3'</code> 或字符串 <code>'local'</code> 或 <code>'utc'</code>。<code>'local'</code> 是工作流的本地时区，这可以在工作流设置中更改。",
  "codeNodeEditor.completer.luxon.instanceMethods.setZone.args.opts": "影响输出的选项。可能的属性：\n<code>keepCalendarTime</code> (boolean)：是否保持时间相同，仅更改偏移量。默认为 false。",
  "codeNodeEditor.completer.luxon.instanceMethods.startOf": "将 DateTime 四舍五入到其一个单位的开头，例如月份的开头",
  "codeNodeEditor.completer.luxon.instanceMethods.startOf.args.unit": "要四舍五入到的开头的单位。<code>year</code>、<code>quarter</code>、<code>month</code>、<code>week</code>、<code>day</code>、<code>hour</code>、<code>minute</code>、<code>second</code> 或 <code>millisecond</code> 之一。",
  "codeNodeEditor.completer.luxon.instanceMethods.startOf.args.opts": "包含影响输出的选项的对象。可能的属性：\n<code>useLocaleWeeks</code> (boolean)：是否在计算周的开始时使用区域设置。默认为 false。",
  "codeNodeEditor.completer.luxon.instanceMethods.toBSON": "返回此 DateTime 的 BSON 可序列化等效项。",
  "codeNodeEditor.completer.luxon.instanceMethods.toFormat": "返回根据指定的格式字符串格式化的此 DateTime 的字符串表示形式。",
  "codeNodeEditor.completer.luxon.instanceMethods.toHTTP": "返回此 DateTime 的字符串表示形式，适用于 HTTP 标头。",
  "codeNodeEditor.completer.luxon.instanceMethods.toISO": "返回此 DateTime 的符合 ISO 8601 的字符串表示形式。",
  "codeNodeEditor.completer.luxon.instanceMethods.toISO.args.opts": '配置选项。有关更多信息，请参阅 <a target="blank" href="https://moment.github.io/luxon/api-docs/index.html#datetimetoiso">Luxon 文档</a>。',
  "codeNodeEditor.completer.luxon.instanceMethods.toISODate": "返回此 DateTime 的日期组成部分的符合 ISO 8601 的字符串表示形式。",
  "codeNodeEditor.completer.luxon.instanceMethods.toISOTime": "返回此 DateTime 的时间组成部分的符合 ISO 8601 的字符串表示形式。",
  "codeNodeEditor.completer.luxon.instanceMethods.toISOWeekDate": "返回此 DateTime 的周日期的符合 ISO 8601 的字符串表示形式。",
  "codeNodeEditor.completer.luxon.instanceMethods.toJSON": "返回此 DateTime 的 ISO 8601 表示形式，适用于 JSON。",
  "codeNodeEditor.completer.luxon.instanceMethods.toJsDate": "返回与此 DateTime 等效的 JavaScript Date。",
  "codeNodeEditor.completer.luxon.instanceMethods.toLocal": "将 DateTime 转换为工作流的本地时区。除非在参数中指定，否则 DateTime 仍表示相同的时刻。工作流的时区可以在工作流设置中设置。",
  "codeNodeEditor.completer.luxon.instanceMethods.toLocal.example": "如果时区为 Europe/Berlin",
  "codeNodeEditor.completer.luxon.instanceMethods.toLocaleParts": "返回格式“部分”的数组，表示单个令牌以及元数据。",
  "codeNodeEditor.completer.luxon.instanceMethods.toLocaleString": "返回表示 DateTime 的本地化字符串，即以其区域设置对应的语言和格式。如果未指定，则默认为系统的区域设置。",
  "codeNodeEditor.completer.luxon.instanceMethods.toLocaleString.args.opts": "渲染的配置选项。有关完整列表，请参阅 <a href=”https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat/DateTimeFormat#parameters”>Intl.DateTimeFormat</a>。默认为渲染短日期。",
  "codeNodeEditor.completer.luxon.instanceMethods.toLocaleString.example": "渲染的配置选项。有关完整列表，请参阅 <a href=”https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat/DateTimeFormat#parameters”>Intl.DateTimeFormat</a>。默认为渲染短日期。",
  "codeNodeEditor.completer.luxon.instanceMethods.toMillis": "以毫秒为单位返回 Unix 时间戳（自 1970 年 1 月 1 日起经过的毫秒数）",
  "codeNodeEditor.completer.luxon.instanceMethods.toObject": "返回 JavaScript 对象，其中包含此 DateTime 的年、月、日等。",
  "codeNodeEditor.completer.luxon.instanceMethods.toRFC2822": "返回此 DateTime 的兼容 RFC 2822 的字符串表示形式，始终使用 UTC。",
  "codeNodeEditor.completer.luxon.instanceMethods.toRelative": "返回相对于当前时间的文本时间表示形式，例如“两天后”。默认情况下向下舍入。",
  "codeNodeEditor.completer.luxon.instanceMethods.toRelative.args.opts": "影响输出的选项。可能的属性：\n<code>unit</code> = 默认单位（<code>years</code>、<code>months</code>、<code>days</code> 等）。\n<code>locale</code> = 要使用的语言和格式（例如，<code>de</code>、<code>fr</code>）",
  "codeNodeEditor.completer.luxon.instanceMethods.toRelativeCalendar": "返回相对于今天的日期字符串表示形式，例如“昨天”或“下个月”。",
  "codeNodeEditor.completer.luxon.instanceMethods.toSQL": "返回此 DateTime 的字符串表示形式，适用于 SQL DateTime。",
  "codeNodeEditor.completer.luxon.instanceMethods.toSQLDate": "返回此 DateTime 的字符串表示形式，适用于 SQL Date。",
  "codeNodeEditor.completer.luxon.instanceMethods.toSQLTime": "返回此 DateTime 的字符串表示形式，适用于 SQL Time。",
  "codeNodeEditor.completer.luxon.instanceMethods.toSeconds": "以秒为单位返回 Unix 时间戳（自 1970 年 1 月 1 日起经过的秒数）",
  "codeNodeEditor.completer.luxon.instanceMethods.toString": "返回 DateTime 的字符串表示形式。类似于 <code>toISO()</code>。有关更多格式化选项，请参阅 <code>format()</code> 或 <code>toLocaleString()</code>。",
  "codeNodeEditor.completer.luxon.instanceMethods.toUTC": "将 DateTime 转换为 UTC 时区。除非在参数中指定，否则 DateTime 仍表示相同的时刻。使用 <code>setZone()</code> 转换为其他时区。",
  "codeNodeEditor.completer.luxon.instanceMethods.toUTC.args.offset": "UTC 的偏移量（以分钟为单位）",
  "codeNodeEditor.completer.luxon.instanceMethods.toUTC.args.opts": "影响输出的选项。可能的属性：\n<code>keepCalendarTime</code> (boolean)：是否保持时间相同，仅更改偏移量。默认为 false。",
  "codeNodeEditor.completer.luxon.instanceMethods.toUnixInteger": "返回此 DateTime 的 epoch 秒（作为整数）。",
  "codeNodeEditor.completer.luxon.instanceMethods.until": "返回此 DateTime 和另一个 DateTime 之间跨越的 Interval。",
  "codeNodeEditor.completer.luxon.instanceMethods.valueOf": "返回此 DateTime 的 epoch 毫秒。",
  "codeNodeEditor.completer.luxon.instanceMethods.weekNumber": "一年中的周数 (1-52 左右)。",
  "codeNodeEditor.completer.luxon.instanceMethods.weekYear": "获取周数年份。",
  "codeNodeEditor.completer.luxon.instanceMethods.weekday": "一周中的某一天。1 是星期一，7 是星期日。",
  "codeNodeEditor.completer.luxon.instanceMethods.weekdayLong": "文本长工作日名称，例如“星期三”。如果未指定，则默认为系统的区域设置。",
  "codeNodeEditor.completer.luxon.instanceMethods.weekdayShort": "文本缩写工作日名称，例如“星期三”。如果未指定，则默认为系统的区域设置。",
  "codeNodeEditor.completer.luxon.instanceMethods.weeksInWeekYear": "返回此 DateTime 所在年份的周数。",
  "codeNodeEditor.completer.luxon.instanceMethods.year": "年份。",
  "codeNodeEditor.completer.luxon.instanceMethods.zone": "与 DateTime 关联的时区",
  "codeNodeEditor.completer.luxon.instanceMethods.zoneName": "获取时区的名称。",
  "codeNodeEditor.completer.selector.all": "@:_reusableBaseText.codeNodeEditor.completer.all",
  "codeNodeEditor.completer.selector.context": "有关节点的额外数据",
  "codeNodeEditor.completer.selector.first": "@:_reusableBaseText.codeNodeEditor.completer.first",
  "codeNodeEditor.completer.selector.item": "返回匹配的项目，即用于生成当前节点中当前项目的项目。",
  "codeNodeEditor.completer.selector.args.branchIndex": "要使用的节点的输出分支。默认为第一个分支（索引 0）",
  "codeNodeEditor.completer.selector.args.runIndex": "要使用的节点的运行。默认为第一次运行（索引 0）",
  "codeNodeEditor.completer.selector.itemMatching": "@:_reusableBaseText.codeNodeEditor.completer.itemMatching",
  "codeNodeEditor.completer.selector.itemMatching.args.currentItemIndex": "要匹配的当前节点中项目的索引。",
  "codeNodeEditor.completer.selector.last": "@:_reusableBaseText.codeNodeEditor.completer.last",
  "codeNodeEditor.completer.selector.params": "给定节点的配置设置。这些是在节点的 UI 中填写的参数（例如，其操作）。",
  "codeNodeEditor.completer.selector.isExecuted": "如果节点已执行，则为 <code>true</code>，否则为 <code>false</code>",
  "codeNodeEditor.completer.section.input": "输入",
  "codeNodeEditor.completer.section.prevNodes": "较早的节点",
  "codeNodeEditor.completer.section.metadata": "元数据",
  "codeNodeEditor.completer.section.fields": "字段",
  "codeNodeEditor.completer.section.properties": "属性",
  "codeNodeEditor.completer.section.methods": "方法",
  "codeNodeEditor.completer.section.otherMethods": "其他方法",
  "codeNodeEditor.completer.section.recommended": "建议",
  "codeNodeEditor.completer.section.recommendedMethods": "建议方法",
  "codeNodeEditor.completer.section.other": "其他",
  "codeNodeEditor.completer.section.edit": "编辑",
  "codeNodeEditor.completer.section.query": "查询",
  "codeNodeEditor.completer.section.format": "格式",
  "codeNodeEditor.completer.section.component": "组件",
  "codeNodeEditor.completer.section.case": "大小写",
  "codeNodeEditor.completer.section.cast": "强制转换",
  "codeNodeEditor.completer.section.compare": "比较",
  "codeNodeEditor.completer.section.validation": "验证",
  "codeNodeEditor.linter.allItems.firstOrLastCalledWithArg": "不应使用参数调用。",
  "codeNodeEditor.linter.allItems.emptyReturn": "代码没有正确返回项目。请返回一个对象数组，每个对象对应您要输出的项目。",
  "codeNodeEditor.linter.allItems.itemCall": "`item` 是要访问的属性，而不是要调用的方法。您是否指的是不带括号的 `.item`？",
  "codeNodeEditor.linter.allItems.itemMatchingNoArg": "`.itemMatching()` 希望传入一个项索引作为其参数。",
  "codeNodeEditor.linter.allItems.unavailableItem": "旧版 `item` 仅在“对每个项目运行一次”模式下可用。",
  "codeNodeEditor.linter.allItems.unavailableProperty": "`.item` 仅在“对每个项目运行一次”模式下可用。请改用 `.first()`。",
  "codeNodeEditor.linter.allItems.unavailableVar": "仅在“对每个项目运行一次”模式下可用。",
  "codeNodeEditor.linter.bothModes.directAccess.firstOrLastCall": "@:_reusableBaseText.codeNodeEditor.linter.useJson",
  "codeNodeEditor.linter.bothModes.directAccess.itemProperty": "@:_reusableBaseText.codeNodeEditor.linter.useJson",
  "codeNodeEditor.linter.bothModes.varDeclaration.itemProperty": "@:_reusableBaseText.codeNodeEditor.linter.useJson",
  "codeNodeEditor.linter.bothModes.varDeclaration.itemSubproperty": "@:_reusableBaseText.codeNodeEditor.linter.useJson",
  "codeNodeEditor.linter.eachItem.emptyReturn": "代码没有返回对象。请返回表示输出项的对象",
  "codeNodeEditor.linter.eachItem.legacyItemAccess": "`item` 是一个旧版变量。请考虑使用 `$input.item`",
  "codeNodeEditor.linter.eachItem.returnArray": "代码没有返回对象。找到了数组。请返回表示输出项的对象",
  "codeNodeEditor.linter.eachItem.unavailableItems": "旧版 `items` 仅在“对所有项目运行一次”模式下可用。",
  "codeNodeEditor.linter.eachItem.unavailableMethod": "方法 `$input.{method}()` 仅在“对所有项目运行一次”模式下可用。",
  "codeNodeEditor.linter.eachItem.preferFirst": "为了 n8n 可以优化执行，请优先使用 `.first()` 而不是 `.item`",
  "codeNodeEditor.linter.bothModes.syntaxError": "语法错误",
  "codeNodeEditor.linter.bothModes.dollarSignVariable": "请使用字符串字面值，而不是变量，以便 n8n 可以优化执行。",
  "codeNodeEditor.askAi.placeholder": "告诉 AI 您希望代码实现什么目标。您可以使用点表示法引用输入数据字段（例如 user.email）",
  "codeNodeEditor.askAi.intro": "你好 AI，生成可以实现以下目的的 JavaScript 代码...",
  "codeNodeEditor.askAi.help": "帮助",
  "codeNodeEditor.askAi.generateCode": "生成代码",
  "codeNodeEditor.askAi.noInputData": "当此节点有传入的输入数据（来自工作流中较早的节点）时，您可以生成代码",
  "codeNodeEditor.askAi.sureLeaveTab": "您确定要切换选项卡吗？代码生成将停止",
  "codeNodeEditor.askAi.areYouSure": "您确定吗？",
  "codeNodeEditor.askAi.switchTab": "切换选项卡",
  "codeNodeEditor.askAi.noPrompt": "首先在生成代码之前输入以上提示",
  "codeNodeEditor.askAi.onlyAllItemsMode": "“询问 AI”生成仅在“对所有项目运行一次”模式下起作用",
  "codeNodeEditor.askAi.promptTooShort": "在尝试生成代码之前，输入至少 {minLength} 个字符",
  "codeNodeEditor.askAi.generateCodeAndReplace": "生成并替换代码",
  "codeNodeEditor.askAi.replaceCurrentCode": "替换当前代码？",
  "codeNodeEditor.askAi.areYouSureToReplace": "您确定要生成新代码吗？当前代码将被替换。",
  "codeNodeEditor.askAi.loadingPhrase0": "AI 齿轮正在运转，即将完成...",
  "codeNodeEditor.askAi.loadingPhrase1": "上上下下左右左右 b a 开始...",
  "codeNodeEditor.askAi.loadingPhrase2": "正在咨询 Jan Oberhauser...",
  "codeNodeEditor.askAi.loadingPhrase3": "正在收集字节和片段...",
  "codeNodeEditor.askAi.loadingPhrase4": "正在检查是否有其他 AI 知道答案...",
  "codeNodeEditor.askAi.loadingPhrase5": "正在检查 Stack Overflow...",
  "codeNodeEditor.askAi.loadingPhrase6": "正在处理数据，AI 风格...",
  "codeNodeEditor.askAi.loadingPhrase7": "请稍候，AI 魔法正在进行...",
  "codeNodeEditor.askAi.generationCompleted": "✨ 代码生成已完成",
  "codeNodeEditor.askAi.generationFailed": "代码生成失败",
  "codeNodeEditor.askAi.generationFailedUnknown": "由于未知原因，代码生成失败。请在几分钟后重试。",
  "codeNodeEditor.askAi.generationFailedWithReason": "代码生成失败，错误信息：{error}。请稍后重试。",
  "codeNodeEditor.askAi.generationFailedDown": "很抱歉，我们的 AI 服务当前不可用。请稍后重试。如果问题仍然存在，请联系支持人员。",
  "codeNodeEditor.askAi.generationFailedRate": "我们已达到与我们的 AI 合作伙伴的速率限制（请求过多）。请等待一分钟再重试。",
  "codeNodeEditor.askAi.generationFailedTooLarge": "您的工作流数据太大，无法供 AI 处理。请简化发送到代码节点的数据，然后重试。",
  "codeNodeEditor.tabs.askAi": "✨ 询问 AI",
  "codeNodeEditor.tabs.code": "代码",
  "codeNodeEditor.examples": "示例",
  "codeNodeEditor.parameters": "参数",
  "codeNodeEditor.optional": "可选",
  "codeNodeEditor.defaultsTo": "默认为 {default}。",
  "collectionParameter.choose": "选择...",
  "collectionParameter.noProperties": "无属性",
  "credentialEdit.credentialConfig.accountConnected": "帐户已连接",
  "credentialEdit.credentialConfig.clickToCopy": "点击复制",
  "credentialEdit.credentialConfig.connectionTestedSuccessfully": "连接测试成功",
  "credentialEdit.credentialConfig.couldntConnectWithTheseSettings": "无法使用这些设置进行连接",
  "credentialEdit.credentialConfig.couldntConnectWithTheseSettings.sharee": "连接设置有问题。{owner} 可能会修复此问题",
  "credentialEdit.credentialConfig.needHelpFillingOutTheseFields": "需要帮助填写这些字段？",
  "credentialEdit.credentialConfig.oAuthRedirectUrl": "OAuth 重定向 URL",
  "credentialEdit.credentialConfig.openDocs": "打开文档",
  "credentialEdit.credentialConfig.pleaseCheckTheErrorsBelow": "请检查下面的错误",
  "credentialEdit.credentialConfig.pleaseCheckTheErrorsBelow.sharee": "连接设置有问题。{owner} 可能会修复此问题",
  "credentialEdit.credentialConfig.reconnect": "重新连接",
  "credentialEdit.credentialConfig.reconnectOAuth2Credential": "重新连接 OAuth2 凭证",
  "credentialEdit.credentialConfig.redirectUrlCopiedToClipboard": "重定向 URL 已复制到剪贴板",
  "credentialEdit.credentialConfig.retry": "重试",
  "credentialEdit.credentialConfig.retryCredentialTest": "重试凭证测试",
  "credentialEdit.credentialConfig.retrying": "正在重试",
  "credentialEdit.credentialConfig.subtitle": "在 {appName} 中，当提示输入 OAuth 回调或重定向 URL 时，请使用上面的 URL",
  "credentialEdit.credentialConfig.theServiceYouReConnectingTo": "您要连接到的服务",
  "credentialEdit.credentialConfig.missingCredentialType": "此凭证的类型不可用。当卸载先前安装的社区或自定义节点时，通常会发生这种情况。",
  "credentialEdit.credentialConfig.authTypeSelectorLabel": "使用以下方式连接",
  "credentialEdit.credentialConfig.authTypeSelectorTooltip": "用于连接的身份验证方法",
  "credentialEdit.credentialConfig.recommendedAuthTypeSuffix": "（推荐）",
  "credentialEdit.credentialConfig.externalSecrets": "企业计划用户可以从外部保管库中提取凭证。",
  "credentialEdit.credentialConfig.externalSecrets.moreInfo": "更多信息",
  "credentialEdit.credentialEdit.confirmMessage.beforeClose1.cancelButtonText": "关闭",
  "credentialEdit.credentialEdit.confirmMessage.beforeClose1.confirmButtonText": "继续编辑",
  "credentialEdit.credentialEdit.confirmMessage.beforeClose1.headline": "关闭而不保存？",
  "credentialEdit.credentialEdit.confirmMessage.beforeClose1.message": "是否确定要丢弃对 {credentialDisplayName} 凭证所做的更改？",
  "credentialEdit.credentialEdit.confirmMessage.beforeClose2.cancelButtonText": "关闭",
  "credentialEdit.credentialEdit.confirmMessage.beforeClose2.confirmButtonText": "继续编辑",
  "credentialEdit.credentialEdit.confirmMessage.beforeClose2.headline": "关闭而不连接？",
  "credentialEdit.credentialEdit.confirmMessage.beforeClose2.message": "您需要连接凭证才能使其工作",
  "credentialEdit.credentialEdit.confirmMessage.deleteCredential.cancelButtonText": "",
  "credentialEdit.credentialEdit.confirmMessage.deleteCredential.confirmButtonText": "是，删除",
  "credentialEdit.credentialEdit.confirmMessage.deleteCredential.headline": "删除凭证？",
  "credentialEdit.credentialEdit.confirmMessage.deleteCredential.message": "是否确定要删除“{savedCredentialName}”？这可能会中断任何使用它的工作流。",
  "credentialEdit.credentialEdit.connection": "连接",
  "credentialEdit.credentialEdit.sharing": "共享",
  "credentialEdit.credentialEdit.couldNotFindCredentialOfType": "找不到类型为的凭证",
  "credentialEdit.credentialEdit.couldNotFindCredentialWithId": "找不到 ID 为的凭证",
  "credentialEdit.credentialEdit.delete": "删除",
  "credentialEdit.credentialEdit.details": "详细信息",
  "credentialEdit.credentialEdit.saving": "正在保存",
  "credentialEdit.credentialEdit.showError.createCredential.title": "创建凭证时出现问题",
  "credentialEdit.credentialEdit.showError.deleteCredential.title": "删除凭证时出现问题",
  "credentialEdit.credentialEdit.showError.generateAuthorizationUrl.message": "生成授权 URL 时出现问题",
  "credentialEdit.credentialEdit.showError.generateAuthorizationUrl.title": "OAuth 授权错误",
  "credentialEdit.credentialEdit.showError.loadCredential.title": "加载凭证时出现问题",
  "credentialEdit.credentialEdit.showError.updateCredential.title": "更新凭证时出现问题",
  "credentialEdit.credentialEdit.showMessage.title": "凭证已删除",
  "credentialEdit.credentialEdit.testing": "正在测试",
  "credentialEdit.credentialEdit.info.sharee": "只有 {credentialOwnerName} 可以编辑此连接",
  "credentialEdit.credentialInfo.allowUseBy": "允许以下用户使用",
  "credentialEdit.credentialInfo.created": "创建时间",
  "credentialEdit.credentialInfo.id": "ID",
  "credentialEdit.credentialInfo.lastModified": "上次修改时间",
  "credentialEdit.credentialEdit.setupGuide": "设置指南",
  "credentialEdit.credentialEdit.docs": "文档",
  "credentialEdit.oAuthButton.connectMyAccount": "连接我的帐户",
  "credentialEdit.oAuthButton.signInWithGoogle": "使用 Google 登录",
  "credentialEdit.credentialSharing.info.owner": "共享凭证允许用户在其工作流中使用它。他们无法访问凭证详细信息。",
  "credentialEdit.credentialSharing.info.sharee.team": "只有具有凭证共享权限的用户才能更改此凭证的共享对象",
  "credentialEdit.credentialSharing.info.sharee.personal": "只有 {credentialOwnerName} 或具有凭证共享权限的用户才能更改此凭证的共享对象",
  "credentialEdit.credentialSharing.info.sharee.fallback": "所有者",
  "credentialEdit.credentialSharing.list.delete": "删除",
  "credentialEdit.credentialSharing.list.delete.confirm.title": "删除访问权限？",
  "credentialEdit.credentialSharing.list.delete.confirm.message": "这可能会中断 {name} 在其中使用此凭证的任何工作流",
  "credentialEdit.credentialSharing.list.delete.confirm.confirmButtonText": "删除",
  "credentialEdit.credentialSharing.list.delete.confirm.cancelButtonText": "取消",
  "credentialEdit.credentialSharing.role.user": "用户",
  "credentialSelectModal.addNewCredential": "添加新凭证",
  "credentialSelectModal.continue": "继续",
  "credentialSelectModal.searchForApp": "搜索应用...",
  "credentialSelectModal.selectAnAppOrServiceToConnectTo": "选择要连接的应用或服务",
  "credentialsList.addNew": "添加新凭证",
  "credentialsList.confirmMessage.cancelButtonText": "",
  "credentialsList.confirmMessage.confirmButtonText": "是，删除",
  "credentialsList.confirmMessage.headline": "删除凭证？",
  "credentialsList.confirmMessage.message": "是否确定要删除 {credentialName}？",
  "credentialsList.created": "已创建",
  "credentialsList.credentials": "凭证",
  "credentialsList.deleteCredential": "删除凭证",
  "credentialsList.editCredential": "编辑凭证",
  "credentialsList.errorLoadingCredentials": "加载凭证时出错",
  "credentialsList.name": "@:_reusableBaseText.name",
  "credentialsList.operations": "操作",
  "credentialsList.showError.deleteCredential.title": "删除凭证时出现问题",
  "credentialsList.showMessage.title": "凭证已删除",
  "credentialsList.type": "类型",
  "credentialsList.updated": "已修改",
  "credentialsList.yourSavedCredentials": "您已保存的凭证",
  "credentials.heading": "凭证",
  "credentials.add": "添加凭证",
  "credentials.project.add": "将凭证添加到项目",
  "credentials.empty.heading": "{name}，让我们设置一个凭证",
  "credentials.empty.heading.userNotSetup": "设置凭证",
  "credentials.empty.description": "凭证允许工作流与您的应用和服务进行交互",
  "credentials.empty.button": "添加第一个凭证",
  "credentials.empty.button.disabled.tooltip": "您在项目中的当前角色不允许您创建凭证",
  "credentials.item.open": "打开",
  "credentials.item.delete": "删除",
  "credentials.item.move": "移动",
  "credentials.item.updated": "修改于",
  "credentials.item.created": "创建于",
  "credentials.item.owner": "所有者",
  "credentials.item.readonly": "只读",
  "credentials.item.needsSetup": "需要首次设置",
  "credentials.search.placeholder": "搜索凭证...",
  "credentials.filters.type": "类型",
  "credentials.filters.setup": "需要首次设置",
  "credentials.filters.status": "状态",
  "credentials.filters.active": "由于应用了筛选器，某些凭证可能处于隐藏状态。",
  "credentials.filters.active.reset": "删除筛选器",
  "credentials.sort.lastUpdated": "按上次更新时间排序",
  "credentials.sort.lastCreated": "按上次创建时间排序",
  "credentials.sort.nameAsc": "按名称排序 (A-Z)",
  "credentials.sort.nameDesc": "按名称排序 (Z-A)",
  "credentials.noResults": "未找到凭证",
  "credentials.noResults.withSearch.switchToShared.preamble": "某些凭证可能处于",
  "credentials.noResults.withSearch.switchToShared.link": "隐藏状态",
  "credentials.create.personal.toast.title": "凭证已成功创建",
  "credentials.create.personal.toast.text": "此凭证已在您的个人空间内创建。",
  "credentials.create.project.toast.title": "凭证已在 {projectName} 中成功创建",
  "credentials.create.project.toast.text": "来自 {projectName} 的所有成员都将有权访问此凭证。",
  "dataDisplay.needHelp": "需要帮助？",
  "dataDisplay.nodeDocumentation": "节点文档",
  "dataDisplay.openDocumentationFor": "打开 {nodeTypeDisplayName} 文档",
  "dataMapping.dragColumnToFieldHint": "拖动到字段以将列映射到该字段",
  "dataMapping.dragFromPreviousHint": "首先点击此按钮，将先前节点中的数据映射到 <b>{name}</b>",
  "dataMapping.success.title": "您刚刚映射了一些数据！",
  "dataMapping.success.moreInfo": '查看我们的 <a href="https://docs.n8n.io/data/data-mapping" target="_blank">文档</a>，了解有关在 n8n 中映射数据的更多详细信息',
  "dataMapping.tableView.tableColumnsExceeded": "某些列已隐藏",
  "dataMapping.tableView.tableColumnsExceeded.tooltip": "您的数据有超过 {columnLimit} 列，因此某些列已隐藏。切换到 {link} 以查看所有数据。",
  "dataMapping.tableView.tableColumnsExceeded.tooltip.link": "JSON 视图",
  "dataMapping.tableView.columnCollapsing": "折叠行",
  "dataMapping.tableView.columnCollapsing.tooltip": "折叠行（以比较此列中的值）",
  "dataMapping.schemaView.emptyData": "无字段 - 项存在，但它们为空",
  "dataMapping.schemaView.emptySchema": "无字段 - 项目存在，但为空",
  "dataMapping.schemaView.emptySchemaWithBinary": "仅存在二进制数据。请使用“二进制”标签页查看。",
  "dataMapping.schemaView.executeSchema": "{link}查看架构",
  "dataMapping.schemaView.disabled": "此节点已禁用，将仅传递数据",
  "dataMapping.schemaView.noMatches": "没有与“{search}”匹配的结果",
  "dataMapping.schemaView.preview": "通常输出以下字段。执行节点以查看实际内容。{link}",
  "dataMapping.schemaView.previewExtraFields": "可能存在更多字段。执行该节点以确认。",
  "dataMapping.schemaView.previewNode": "预览",
  "dataMapping.schemaView.variablesContextTitle": "变量与上下文",
  "dataMapping.schemaView.execution.resumeUrl": '"Wait"节点的恢复URL',
  "dataMapping.schemaView.variablesUpgrade": '在专业版或企业版计划中设置全局变量，并在不同工作流程中使用它们。<a href="https://docs.n8n.io/environments/variables/" target="_blank">详情</a>',
  "dataMapping.schemaView.variablesEmpty": '在此处<a href="/variables" target="_blank">创建可跨工作流程使用的变量</a>',
  "displayWithChange.cancelEdit": "取消编辑",
  "displayWithChange.clickToChange": "点击更改",
  "displayWithChange.setValue": "设置值",
  "duplicateWorkflowDialog.cancel": "@:_reusableBaseText.cancel",
  "duplicateWorkflowDialog.chooseOrCreateATag": "选择或创建标签",
  "duplicateWorkflowDialog.duplicateWorkflow": "复制工作流",
  "duplicateWorkflowDialog.enterWorkflowName": "输入工作流名称",
  "duplicateWorkflowDialog.save": "复制",
  "duplicateWorkflowDialog.errors.missingName.title": "缺少名称",
  "duplicateWorkflowDialog.errors.missingName.message": "请输入名称。",
  "duplicateWorkflowDialog.errors.forbidden.title": "复制工作流失败",
  "duplicateWorkflowDialog.errors.forbidden.message": "此操作被禁止。您是否具有正确的权限？",
  "duplicateWorkflowDialog.errors.generic.title": "复制工作流失败",
  "editor.mainHeader.githubButton.label": "在GitHub上为n8n-io/n8n项目点赞",
  error,
  "error.goBack": "返回",
  "error.pageNotFound": "糟糕，找不到该页",
  "error.entityNotFound.title": "未找到 {entity}",
  "error.entityNotFound.text": "我们找不到您正在寻找的 {entity}。请确保您输入的网址正确。",
  "error.entityNotFound.action": "前往概览",
  "error.entityUnAuthorized.title": "你需要权限。",
  "error.entityUnAuthorized.content": "您没有权限查看此 {entity}。请联系分享此链接的人请求访问权限。",
  "executions.ExecutionStatus": "执行状态",
  "executions.concurrency.docsLink": "https://docs.n8n.io/hosting/scaling/concurrency-control/",
  "executionDetails.additionalActions": "附加操作",
  "executionDetails.confirmMessage.confirmButtonText": "是，删除",
  "executionDetails.confirmMessage.headline": "删除执行？",
  "executionDetails.confirmMessage.message": "是否确定要删除当前执行？",
  "executionDetails.confirmMessage.annotationsNote": "删除此项也会删除关联的注释数据。",
  "executionDetails.deleteExecution": "删除此执行",
  "executionDetails.executionFailed": "执行失败",
  "executionDetails.executionFailed.recoveredNodeTitle": "无法显示数据",
  "executionDetails.executionFailed.recoveredNodeMessage": "执行被中断，因此数据未保存。请尝试修复工作流并重新执行。",
  "executionDetails.executionId": "执行 ID",
  "executionDetails.executionWaiting": "执行等待",
  "executionDetails.executionWasSuccessful": "执行成功",
  "executionDetails.of": "的",
  "executionDetails.at": "于",
  "executionDetails.newMessage": "执行正在队列中等待。",
  "executionDetails.openWorkflow": "打开工作流",
  "executionDetails.readOnly.readOnly": "只读",
  "executionDetails.readOnly.youreViewingTheLogOf": "您正在查看先前执行的日志。您无法<br />\n		进行更改，因为此执行已发生。通过点击左侧的名称<br />\n		来更改此工作流。",
  "executionDetails.retry": "重试执行",
  "executionDetails.runningTimeFinished": "在 {time} 内",
  "executionDetails.runningTimeRunning": "持续",
  "executionDetails.runningMessage": "执行正在运行。完成后将在此处显示。",
  "executionDetails.startingSoon": "即将开始",
  "executionDetails.workflow": "工作流",
  "executionsLandingPage.emptyState.noTrigger.heading": "设置第一步。然后执行您的工作流",
  "executionsLandingPage.emptyState.noTrigger.buttonText": "添加第一步...",
  "executionsLandingPage.clickExecutionMessage": "点击列表中的执行以查看它",
  "executionsLandingPage.emptyState.heading": "此处还没有任何内容",
  "executionsLandingPage.emptyState.message": "新的工作流执行将在此处显示",
  "executionsLandingPage.emptyState.accordion.title": "此工作流正在保存哪些执行？",
  "executionsLandingPage.emptyState.accordion.titleWarning": "某些执行将不会被保存",
  "executionsLandingPage.emptyState.accordion.productionExecutions": "生产执行",
  "executionsLandingPage.emptyState.accordion.testExecutions": "测试执行",
  "executionsLandingPage.emptyState.accordion.productionExecutionsWarningTooltip": '并非所有生产执行都会被保存。在工作流的 <a href="#">设置</a>中更改此项',
  "executionsLandingPage.emptyState.accordion.footer": "您可以在以下位置更改此项",
  "executionsLandingPage.emptyState.accordion.footer.settingsLink": "工作流设置",
  "executionsLandingPage.emptyState.accordion.footer.tooltipLink": "保存您的工作流",
  "executionsLandingPage.emptyState.accordion.footer.tooltipText": "以便访问工作流设置",
  "executionsLandingPage.noResults": "未找到执行",
  "executionsList.activeExecutions.none": "无活动执行",
  "executionsList.activeExecutions.header": "{running}/{cap} 个活动执行",
  "executionsList.activeExecutions.tooltip": "当前活动执行：{running} / {cap}。此实例最多只能同时进行 {cap} 次生产执行。",
  "executionsList.activeExecutions.evaluationNote": "评估运行会显示在执行列表中，但不会计入您的执行并发数。",
  "executionsList.allWorkflows": "所有工作流",
  "executionsList.anyStatus": "任何状态",
  "executionsList.autoRefresh": "自动刷新",
  "executionsList.canceled": "已取消",
  "executionsList.confirmMessage.cancelButtonText": "",
  "executionsList.confirmMessage.confirmButtonText": "是，删除",
  "executionsList.confirmMessage.headline": "删除执行？",
  "executionsList.confirmMessage.message": "是否确定要删除 {count} 个选定的执行？",
  "executionsList.confirmMessage.annotationsNote": "删除这些执行也会删除关联的注释数据。",
  "executionsList.confirmMessage.annotatedExecutionMessage": "删除此项也会删除关联的注释数据。是否确定要删除选定的执行？",
  "executionsList.clearSelection": "清除选择",
  "executionsList.error": "错误",
  "executionsList.filters": "筛选器",
  "executionsList.loadMore": "加载更多",
  "executionsList.empty": "无执行",
  "executionsList.loadedAll": "没有更多要提取的执行",
  "executionsList.modes.error": "错误",
  "executionsList.modes.integrated": "已集成",
  "executionsList.modes.manual": "手动",
  "executionsList.modes.retry": "重试",
  "executionsList.modes.trigger": "触发器",
  "executionsList.modes.webhook": "Webhook",
  "executionsList.name": "@:_reusableBaseText.name",
  "executionsList.new": "已排队",
  "executionsList.openPastExecution": "打开过去的执行",
  "executionsList.retryExecution": "重试执行",
  "executionsList.retryOf": "重试",
  "executionsList.retryWithCurrentlySavedWorkflow": "使用当前保存的工作流重试（来自出错的节点）",
  "executionsList.retryWithOriginalWorkflow": "使用原始工作流重试（来自出错的节点）",
  "executionsList.running": "正在运行",
  "executionsList.succeeded": "已成功",
  "executionsList.selectStatus": "选择状态",
  "executionsList.selectWorkflow": "选择工作流",
  "executionsList.selected": "已选择 {count} 个执行：| 已选择 {count} 个执行：",
  "executionsList.selectAll": "选择 {executionNum} 个已完成的执行 | 选择所有 {executionNum} 个已完成的执行",
  "executionsList.test": "测试执行",
  "executionsList.evaluation": "评估执行",
  "executionsList.showError.handleDeleteSelected.title": "删除执行时出现问题",
  "executionsList.showError.loadMore.title": "加载执行时出现问题",
  "executionsList.showError.loadWorkflows.title": "加载工作流时出现问题",
  "executionsList.showError.refreshData.title": "加载数据时出现问题",
  "executionsList.showError.retryExecution.title": "重试时出现问题",
  "executionsList.showError.stopExecution.title": "停止执行时出现问题",
  "executionsList.showMessage.handleDeleteSelected.title": "执行已删除",
  "executionsList.showMessage.retryError.title": "重试失败",
  "executionsList.showMessage.retrySuccess.title": "重试成功",
  "executionsList.showMessage.retryWaiting.title": "重试等待",
  "executionsList.showMessage.retryCrashed.title": "重试失败的",
  "executionsList.showMessage.retryCanceled.title": "重试已取消",
  "executionsList.showMessage.retryRunning.title": "重新尝试运行",
  "executionsList.showMessage.stopExecution.message": "执行 ID {activeExecutionId}",
  "executionsList.showMessage.stopExecution.title": "执行已停止",
  "executionsList.startedAt": "开始时间",
  "executionsList.trigger": "触发于",
  "executionsList.runTime": "运行时间",
  "executionsList.startingSoon": "即将开始",
  "executionsList.started": "{date}，{time}",
  "executionsList.id": "执行 ID",
  "executionsList.status": "状态",
  "executionsList.statusCanceled": "已取消",
  "executionsList.statusText": "{status}，耗时 {time}",
  "executionsList.statusTextWithoutTime": "{status}",
  "executionsList.statusRunning": "{status}，持续 {time}",
  "executionsList.statusWaiting": "{status}，直至 {time}",
  "executionsList.statusUnknown": "无法完成",
  "executionsList.stopExecution": "停止执行",
  "executionsList.success": "成功",
  "executionsList.successRetry": "成功重试",
  "executionsList.unknown": "无法完成",
  "executionsList.unsavedWorkflow": "[未保存的工作流]",
  "executionsList.waiting": "正在等待",
  "executionsList.workflowExecutions": "执行",
  "executionsList.view": "查看",
  "executionsList.stop": "停止",
  "executionsList.statusTooltipText.waitingForWebhook": "工作流正在无限期地等待传入的 Webhook 调用。",
  "executionsList.statusTooltipText.waitingForConcurrencyCapacity": "此执行将在并发容量可用后开始。{instance}",
  "executionsList.statusTooltipText.waitingForConcurrencyCapacity.cloud": "您的计划限制最多 {concurrencyCap} 次并发生产执行。{link}",
  "executionsList.statusTooltipText.waitingForConcurrencyCapacity.self": "此实例最多只能同时进行 {concurrencyCap} 次生产执行。{link}",
  "executionsList.statusTooltipText.theWorkflowIsWaitingIndefinitely": "工作流正在无限期地等待传入的 Webhook 调用。",
  "executionsList.debug.button.copyToEditor": "复制到编辑器",
  "executionsList.debug.button.debugInEditor": "在编辑器中调试",
  "executionsList.debug.paywall.title": "升级以在编辑器中访问调试功能",
  "executionsList.debug.paywall.content": "使用编辑器中的实际固定数据，可以调试以前的执行。",
  "executionsList.debug.paywall.subContent": "它在我们的云计划、企业版以及以下版本中可用。",
  "executionsList.debug.paywall.link.text": "在文档中阅读更多内容",
  "executionsList.debug.paywall.link.url": "https://docs.n8n.io/workflows/executions/debug/",
  "fromAiParametersModal.title": "测试 {nodeName}",
  "fromAiParametersModal.execute": "执行步骤",
  "fromAiParametersModal.description": "提供通常来自“{parentNodeName}”节点的数据",
  "fromAiParametersModal.cancel": "取消",
  "workerList.pageTitle": "工作程序",
  "workerList.empty": "没有响应或可用的工作程序",
  "workerList.item.lastUpdated": "上次更新时间",
  "workerList.item.jobList.empty": "当前无作业",
  "workerList.item.jobListTitle": "当前作业",
  "workerList.item.netListTitle": "网络接口",
  "workerList.item.chartsTitle": "性能监控",
  "workerList.item.copyAddressToClipboard": "已将地址复制到剪贴板",
  "workerList.actionBox.title": "在企业版计划中可用",
  "workerList.actionBox.description": "查看连接到您实例的工作程序的当前状态。",
  "workerList.actionBox.description.link": "更多信息",
  "workerList.actionBox.buttonText": "查看计划",
  "workerList.docs.url": "https://docs.n8n.io/hosting/scaling/queue-mode/#view-running-workers",
  "executionSidebar.executionName": "执行 {id}",
  "executionSidebar.searchPlaceholder": "搜索执行...",
  "executionView.onPaste.title": "无法在此处粘贴",
  "executionView.onPaste.message": "此视图是只读的。切换到“工作流”选项卡以编辑当前工作流",
  "executionView.notFound.message": "找不到 ID 为“{executionId}”的执行！",
  "executionAnnotationView.data.notFound": '通过将 <a target="_blank" href="https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.executiondata/">执行数据</a>节点添加到您的工作流中，在此处显示执行中的重要数据',
  "executionAnnotationView.vote.error": "无法保存注释投票",
  "executionAnnotationView.tag.error": "无法保存注释标签",
  "executionAnnotationView.addTag": "添加标签",
  "executionAnnotationView.chooseOrCreateATag": "选择或创建标签",
  "executionsFilter.annotation.tags": "执行标签",
  "executionsFilter.annotation.rating": "评分",
  "executionsFilter.annotation.rating.all": "任何评分",
  "executionsFilter.annotation.rating.good": "好",
  "executionsFilter.annotation.rating.bad": "不好",
  "executionsFilter.annotation.selectVoteFilter": "选择评分",
  "executionsFilter.selectStatus": "选择状态",
  "executionsFilter.selectWorkflow": "选择工作流",
  "executionsFilter.start": "执行开始时间",
  "executionsFilter.startDate": "最早",
  "executionsFilter.endDate": "最新",
  "executionsFilter.savedData": "高亮显示的数据",
  "executionsFilter.savedDataExactMatch": "完全匹配",
  "executionsFilter.savedDataKey": "键",
  "executionsFilter.savedDataKeyPlaceholder": "ID",
  "executionsFilter.savedDataValue": "值（精确匹配）",
  "executionsFilter.savedDataValuePlaceholder": "123",
  "executionsFilter.reset": "重置所有",
  "executionsFilter.customData.inputTooltip": "升级计划以按运行时设置的自定义数据筛选执行。{link}",
  "executionsFilter.customData.inputTooltip.link": "查看计划",
  "executionsFilter.customData.docsTooltip": "使用“执行数据”节点按您已保存到其中的数据筛选执行。{link}",
  "executionsFilter.customData.docsTooltip.link": "更多信息",
  "expressionEdit.anythingInside": "内部的任何内容 ",
  "expressionEdit.isJavaScript": " 是 JavaScript。",
  "expressionEdit.learnMore": "了解更多",
  "expressionEdit.editExpression": "编辑表达式",
  "expressionEdit.expression": "表达式",
  "expressionEdit.resultOfItem1": "项目 1 的结果",
  "expressionEditor.uncalledFunction": "[这是一个函数，请添加 ()]",
  "expressionModalInput.empty": "[空]",
  "expressionModalInput.undefined": "[未定义]",
  "expressionModalInput.null": "null",
  "expressionTip.noExecutionData": "执行先前的节点以使用输入数据",
  "expressionTip.typeDotPrimitive": '键入 <code>.</code> 以获取数据转换选项。<a target="_blank" href="https://docs.n8n.io/code/builtin/data-transformation-functions/">了解更多</a>',
  "expressionTip.typeDotObject": '键入 <code>.</code> 以获取数据转换选项，或访问字段。<a target="_blank" href="https://docs.n8n.io/code/builtin/data-transformation-functions/">了解更多</a>',
  "expressionTip.javascript": `<code>{'{{ }}'}</code> 中的任何内容都是 JavaScript。<a target="_blank" href="https://docs.n8n.io/code-examples/expressions/">了解更多</a>`,
  "expressionModalInput.noExecutionData": "执行先前的节点以进行预览",
  "expressionModalInput.noNodeExecutionData": "执行节点“{node}”以进行预览",
  "expressionModalInput.noInputConnection": "未连接输入",
  "expressionModalInput.pairedItemConnectionError": "没有回到节点的路径",
  "expressionModalInput.pairedItemInvalidPinnedError": "取消固定节点“{node}”并执行",
  "expressionModalInput.pairedItemError": "无法确定使用哪个项目",
  "expressionModalInput.pairedItemError.noRunData": "无法确定使用哪个项 - 执行节点以获取更多信息",
  "fixedCollectionParameter.choose": "选择...",
  "fixedCollectionParameter.currentlyNoItemsExist": "当前不存在任何项目",
  "fixedCollectionParameter.deleteItem": "删除项目",
  "fixedCollectionParameter.dragItem": "拖动项目",
  "fixedCollectionParameter.moveDown": "向下移动",
  "fixedCollectionParameter.moveUp": "向上移动",
  forgotPassword,
  "forgotPassword.emailSentIfExists": "我们已向 {email} 发送了电子邮件（如果存在匹配的帐户）",
  "forgotPassword.getRecoveryLink": "给我发送一个恢复链接",
  "forgotPassword.noSMTPToSendEmailWarning": "请联系您的管理员。n8n 当前未设置为发送电子邮件。",
  "forgotPassword.recoverPassword": "恢复密码",
  "forgotPassword.recoveryEmailSent": "已发送恢复电子邮件",
  "forgotPassword.returnToSignIn": "返回登录",
  "forgotPassword.sendingEmailError": "发送电子邮件时出现问题",
  "forgotPassword.ldapUserPasswordResetUnavailable": "请联系您的 LDAP 管理员以重置您的密码",
  "forgotPassword.oidcUserPasswordResetUnavailable": "请联系您的 OIDC 管理员重置您的密码。",
  "forgotPassword.smtpErrorContactAdministrator": "请联系您的管理员（您的 SMTP 设置有问题）",
  "forgotPassword.tooManyRequests": "您已达到密码重置限制。请在几分钟后重试。",
  "forms.resourceFiltersDropdown.filters": "筛选器",
  "forms.resourceFiltersDropdown.owner": "所有者",
  "forms.resourceFiltersDropdown.owner.placeholder": "按所有者筛选",
  "forms.resourceFiltersDropdown.reset": "重置所有",
  "folders.actions.create": "在内部创建文件夹",
  "folders.actions.create.workflow": "内部创建流程",
  "folders.actions.moveToFolder": "移动到文件夹",
  "folders.add": "添加文件夹",
  "folders.add.here.message": "在此处新建文件夹",
  "folders.add.to.parent.message": "在“{parent}”中创建文件夹",
  "folders.add.overview.community.message": "个人空间中可用的文件夹",
  "folders.add.overview.withProjects.message": "项目或个人空间中可用的文件夹",
  "folders.add.success.title": "文件夹已创建",
  "folders.add.success.message": '已创建新文件夹“{folderName}”<br><a href="{link}">打开文件夹</a>',
  "folders.invalidName.empty.message": "文件夹名称不能为空",
  "folders.invalidName.tooLong.message": "文件夹名称不得超过 {maxLength} 个字符",
  "folders.invalidName.invalidCharacters.message": "文件夹名称不能包含以下字符：{illegalChars}",
  "folders.invalidName.starts.with.dot..message": "文件夹名称不能以点开头",
  "folders.invalidName.only.dots.message": "文件夹名称不能仅包含点号",
  "folders.delete.confirm.title": "删除“{folderName}”",
  "folders.delete.typeToConfirm": "删除 {folderName}",
  "folders.delete.confirm.message": "您确定要删除此文件夹吗？",
  "folders.delete.success.message": "文件夹已删除",
  "folder.delete.modal.confirmation": "对于这个文件夹中的{folders} {workflows}，我们应该如何处理？",
  "folder.count": "{count}个文件夹",
  "workflow.count": "{count} 个工作流程",
  "folder.and.workflow.separator": "和",
  "folders.delete.action": "归档所有工作流程并删除子文件夹",
  "folders.delete.error.message": "删除文件夹时出现问题",
  "folders.delete.confirmation.message": "输入“delete {folderName}”以确认",
  "folders.transfer.confirm.message": "数据已传输至“{folderName}”",
  "folders.transfer.action": "将工作流和子文件夹转移到“{projectName}”内的另一个文件夹",
  "folders.transfer.action.noProject": "将工作流程和子文件夹转移到另一个文件夹",
  "folders.transfer.selectFolder": "传输至文件夹",
  "folders.transfer.select.placeholder": "选择文件夹",
  "folders.rename.message": "重命名“{folderName}”",
  "folders.rename.error.title": "重命名文件夹时出现问题",
  "folders.rename.success.message": "文件夹已重命名",
  "folders.rename.placeholder": "输入新文件夹名称",
  "folders.not.found.message": "找不到文件夹",
  "folders.move.modal.folder.count": "{count} 个文件夹",
  "folders.move.modal.workflow.count": "{count} 个工作流程",
  "folders.move.modal.title": "移动{resourceTypeLabel} {folderName}",
  "folders.move.modal.description": "这也将移动{folders}{workflows}。",
  "folders.move.modal.confirm": "移动{resourceTypeLabel}",
  "folders.move.modal.project.label": "项目或用户",
  "folders.move.modal.project.placeholder": "选择一个项目或用户",
  "folders.move.modal.folder.label": "文件夹",
  "folders.move.modal.folder.placeholder": "选择一个文件夹",
  "folders.move.modal.folder.noData.label": "未找到文件夹",
  "folders.move.modal.message.usedCredentials.workflow": "同时共享此工作流使用的{usedCredentials}，以确保其持续正常运行。",
  "folders.move.modal.message.usedCredentials.folder": "同时分享这些工作流所使用的{usedCredentials}，以确保它们能够持续正常运行。",
  "folders.move.modal.message.usedCredentials.warning": "如果您选择不共享凭据，工作流程可能无法正确执行。",
  "folders.move.success.title": "成功移动文件夹",
  "folders.move.success.message": '<b>{folderName}</b> 文件夹及其所有工作流和子文件夹已被移动至 <b>{newFolderName}</b>。<br/><br/><a href="{link}">查看 {newFolderName}</a>',
  "folders.move.success.messageNoAccess": "<b>{folderName}</b> 已移动到 <b>{newFolderName}</b>，包括其所有工作流和子文件夹。",
  "folders.move.error.title": "移动文件夹时出现问题",
  "folders.move.workflow.error.title": "工作流移动问题",
  "folders.move.workflow.success.title": "工作流移动成功",
  "folders.move.workflow.success.message": '<b>{workflowName}</b> 已被移至 <b>{newFolderName}</b>。<br/><br/><a href="{link}">查看 {newFolderName}</a>',
  "folders.move.workflow.success.messageNoAccess": "<b>{workflowName}</b> 已移动到 <b>{newFolderName}</b>。",
  "folders.move.project.root.name": "无文件夹（项目根目录）",
  "folders.open.error.title": "无法打开文件夹",
  "folders.create.error.title": "创建文件夹时出现问题",
  "folders.empty.actionbox.title": "文件夹“{folderName}”中尚无内容",
  "folders.registeredCommunity.cta.heading": "获取已注册社区的文件夹访问权限",
  "folders.breadcrumbs.noTruncated.message": "路径中无隐藏项",
  "generic.oauth1Api": "OAuth1 API",
  "generic.oauth2Api": "OAuth2 API",
  "genericHelpers.loading": "正在加载",
  "genericHelpers.hrsShort": "h",
  "genericHelpers.min": "分钟",
  "genericHelpers.minShort": "分",
  "genericHelpers.sec": "秒",
  "genericHelpers.secShort": "秒",
  "genericHelpers.millis": "毫秒",
  "readOnly.showMessage.executions.message": "执行是只读的。在“<b>工作流</b>”选项卡中进行更改。",
  "readOnly.showMessage.executions.title": "无法编辑执行",
  "readOnlyEnv.showMessage.executions.message": "执行是只读的。",
  "readOnlyEnv.showMessage.executions.title": "无法编辑执行",
  "readOnlyEnv.showMessage.workflows.message": "在受保护的实例中，工作流是只读的。",
  "readOnlyEnv.showMessage.workflows.title": "无法编辑工作流",
  "readOnlyEnv.tooltip": "这是一个受保护的实例，其中修改受到限制。{link}",
  "readOnlyEnv.tooltip.link": "更多信息。",
  "readOnlyEnv.cantAdd.workflow": "您无法在受保护的 n8n 实例上添加新的工作流",
  "readOnlyEnv.cantAdd.credential": "您无法在受保护的 n8n 实例上添加新的凭证",
  "readOnlyEnv.cantAdd.project": "在受保护的n8n实例中无法添加新项目",
  "readOnlyEnv.cantAdd.any": "您无法在受保护的 n8n 实例上创建新的工作流或凭证",
  "readOnlyEnv.cantEditOrRun": "此工作流无法编辑或手动运行，因为它位于受保护的实例上",
  "logs.overview.header.title": "日志",
  "logs.overview.header.actions.clearExecution": "明确执行",
  "logs.overview.header.actions.clearExecution.tooltip": "清除执行数据",
  "logs.overview.header.switch.details": "详情",
  "logs.overview.header.switch.overview": "概览",
  "logs.overview.body.empty.message": "暂无显示内容。请执行工作流以查看运行日志。",
  "logs.overview.body.empty.action": "执行工作流程",
  "logs.overview.body.summaryText.for": "{status} 持续 {time}",
  "logs.overview.body.summaryText.in": "{status}于{time}",
  "logs.overview.body.started": "开始于{time}",
  "logs.overview.body.run": "执行步骤",
  "logs.overview.body.open": "打开...",
  "logs.overview.body.toggleRow": "切换行",
  "logs.details.header.actions.input": "输入",
  "logs.details.header.actions.output": "输出",
  "logs.details.body.itemCount": "{count} 项 | {count} 项",
  "logs.details.body.multipleInputs": "多个输入。通过{button}查看它们",
  "logs.details.body.multipleInputs.openingTheNode": "打开节点",
  "mainSidebar.aboutN8n": "关于 n8n",
  "mainSidebar.confirmMessage.workflowArchive.cancelButtonText": "限制：仅输出翻译内容，不包含任何额外信息或解释。",
  "mainSidebar.confirmMessage.workflowArchive.confirmButtonText": "是的，存档",
  "mainSidebar.confirmMessage.workflowArchive.headline": "存档工作流程？",
  "mainSidebar.confirmMessage.workflowArchive.message": "您确定要归档“{workflowName}”吗？",
  "mainSidebar.confirmMessage.workflowDelete.cancelButtonText": "",
  "mainSidebar.confirmMessage.workflowDelete.confirmButtonText": "是，删除",
  "mainSidebar.confirmMessage.workflowDelete.headline": "删除工作流？",
  "mainSidebar.confirmMessage.workflowDelete.message": "您确定要永久删除“{workflowName}”吗？",
  "mainSidebar.credentials": "凭证",
  "mainSidebar.variables": "变量",
  "mainSidebar.help": "帮助",
  "mainSidebar.helpMenuItems.course": "课程",
  "mainSidebar.helpMenuItems.documentation": "文档",
  "mainSidebar.helpMenuItems.forum": "论坛",
  "mainSidebar.helpMenuItems.quickstart": "快速入门",
  "mainSidebar.helpMenuItems.reportBug": "报告错误",
  "mainSidebar.new": "新建",
  "mainSidebar.newTemplate": "从模板新建",
  "mainSidebar.open": "打开",
  "mainSidebar.prompt.cancel": "@:_reusableBaseText.cancel",
  "mainSidebar.prompt.import": "导入",
  "mainSidebar.prompt.importWorkflowFromUrl": "从 URL 导入工作流",
  "mainSidebar.prompt.invalidUrl": "无效的 URL",
  "mainSidebar.prompt.workflowUrl": "工作流 URL",
  "mainSidebar.save": "@:_reusableBaseText.save",
  "mainSidebar.showError.stopExecution.title": "停止执行时出现问题",
  "mainSidebar.showMessage.handleFileImport.message": "该文件不包含有效的 JSON 数据",
  "mainSidebar.showMessage.handleFileImport.title": "无法导入文件",
  "mainSidebar.showMessage.handleSelect1.title": "工作流 '{workflowName}' 已删除",
  "mainSidebar.showMessage.handleSelect2.title": "工作流已创建",
  "mainSidebar.showMessage.handleSelect3.title": "工作流已创建",
  "mainSidebar.showMessage.handleArchive.title": "工作流“{workflowName}”已归档",
  "mainSidebar.showMessage.handleUnarchive.title": "工作流'{workflowName}'未归档",
  "mainSidebar.showMessage.stopExecution.title": "执行已停止",
  "mainSidebar.templates": "模板",
  "mainSidebar.workflows": "工作流",
  "mainSidebar.workflows.readOnlyEnv.tooltip": "受保护的实例阻止编辑工作流（建议用于生产环境）。{link}",
  "mainSidebar.workflows.readOnlyEnv.tooltip.link": "更多信息",
  "mainSidebar.executions": "执行",
  "mainSidebar.workersView": "工作程序",
  "mainSidebar.whatsNew": "最新消息",
  "mainSidebar.whatsNew.fullChangelog": "完整变更日志",
  "menuActions.duplicate": "复制",
  "menuActions.download": "下载",
  "menuActions.push": "推送到 Git",
  "menuActions.importFromUrl": "从 URL 导入...",
  "menuActions.importFromFile": "从文件导入...",
  "menuActions.delete": "删除",
  "menuActions.archive": "归档",
  "menuActions.unarchive": "解档",
  "multipleParameter.addItem": "添加项目",
  "multipleParameter.currentlyNoItemsExist": "当前不存在任何项目",
  "multipleParameter.deleteItem": "删除项目",
  "multipleParameter.moveDown": "向下移动",
  "multipleParameter.moveUp": "向上移动",
  "ndv.backToCanvas": "返回画布",
  "ndv.backToCanvas.waitingForTriggerWarning": "正在等待触发节点执行。关闭此视图以查看工作流画布。",
  "ndv.close.tooltip": "数据已存储，可以安全关闭。",
  "ndv.execute.testNode": "执行步骤",
  "ndv.execute.testNode.description": "运行当前节点。如果先前的节点尚未运行，则也会运行它们",
  "ndv.execute.generateCodeAndTestNode.description": "生成代码，然后运行当前节点",
  "ndv.execute.generateCode.message": "“{nodeName}”中的说明已更改",
  "ndv.execute.generateCode.title": "正在生成转换代码",
  "ndv.execute.executing": "正在执行",
  "ndv.execute.fetchEvent": "提取测试事件",
  "ndv.execute.fixPrevious": "首先修复上一个节点",
  "ndv.execute.generatingCode": "根据指令生成代码",
  "ndv.execute.listenForTestEvent": "监听测试事件",
  "ndv.execute.testChat": "测试聊天",
  "ndv.execute.testStep": "执行步骤",
  "ndv.execute.stopListening": "停止监听",
  "ndv.execute.nodeIsDisabled": "启用节点以执行",
  "ndv.execute.requiredFieldsMissing": "首先完成必填字段",
  "ndv.execute.stopWaitingForWebhook.error": "删除测试 Webhook 时出现问题",
  "ndv.execute.workflowAlreadyRunning": "工作流已在运行",
  "ndv.execute.deactivated": "该节点已停用，无法运行",
  "ndv.featureRequest": "我希望此节点可以...",
  "ndv.input": "输入",
  "ndv.input.nodeDistance": "返回 {count} 个节点 | 返回 {count} 个节点",
  "ndv.input.noNodesFound": "未找到节点",
  "ndv.input.mapping": "映射",
  "ndv.input.fromAI": "来自 AI",
  "ndv.input.parentNodes": "父节点",
  "ndv.input.tooMuchData.title": "显示数据？",
  "ndv.input.noOutputDataInBranch": "此分支中无输入数据",
  "ndv.input.noOutputDataInNode": "节点未输出任何数据。当节点没有输出数据时，n8n 会停止执行工作流。",
  "ndv.input.noOutputData": "无数据",
  "ndv.input.noOutputData.executePrevious": "执行先前的节点",
  "ndv.input.noOutputData.title": "尚未有输入数据",
  "ndv.input.noOutputData.v2.title": "无输入数据",
  "ndv.input.noOutputData.v2.description": "查看输入数据 {link}",
  "ndv.input.noOutputData.v2.action": "执行前置节点",
  "ndv.input.noOutputData.v2.tooltip": "从最早的未执行节点，或已执行但之后发生改变的节点开始",
  "ndv.input.noOutputData.hint": "（来自尚未输出数据的最早节点）",
  "ndv.input.noOutputData.hint.tooltip": "从最早尚未执行的节点开始，或者从虽已执行但之后发生了变更的节点开始",
  "ndv.input.noOutputData.schemaPreviewHint": "切换到 {schema} 以使用架构预览",
  "ndv.input.noOutputData.or": "或",
  "ndv.input.executingPrevious": "正在执行先前的节点...",
  "ndv.input.notConnected.title": "连线我",
  "ndv.input.notConnected.v2.title": "未连接任何输入",
  "ndv.input.notConnected.v2.description": "此节点只有连接到其他节点时才能接收输入数据。{link}",
  "ndv.input.notConnected.message": "仅当您将此节点连接到另一个节点时，此节点才能接收输入数据。",
  "ndv.input.notConnected.learnMore": "了解更多",
  "ndv.input.disabled": "“{nodeName}”节点已禁用，不会执行。",
  "ndv.input.disabled.cta": "启用它",
  "ndv.input.rootNodeHasNotRun.title": "父节点尚未运行",
  "ndv.input.rootNodeHasNotRun.description": "父节点传递给此节点的输入将显示在此处。如需从先前节点映射数据，请使用{link}视图。",
  "ndv.input.rootNodeHasNotRun.description.link": "映射",
  "ndv.output": "输出",
  "ndv.output.ai.empty": "👈 使用这些日志查看有关 {node} 节点如何完成处理的信息。您可以点击节点以查看它接收的输入和输出数据。",
  "ndv.output.ai.waiting": "正在等待消息",
  "ndv.output.outType.logs": "日志",
  "ndv.output.outType.regular": "输出",
  "ndv.output.edit": "编辑输出",
  "ndv.output.all": "全部",
  "ndv.output.branch": "分支",
  "ndv.output.executing": "正在执行节点...",
  "ndv.output.items": "{count} 个项目 | {count} 个项目",
  "ndv.output.andSubExecutions": "，{count} 个子执行 | ，{count} 个子执行",
  "ndv.output.noOutputData.message": "当节点没有输出数据时，n8n 会停止执行工作流。您可以通过以下方式更改此默认行为：",
  "ndv.output.noOutputData.message.settings": "设置",
  "ndv.output.noOutputData.message.settingsOption": "> “始终输出数据”。",
  "ndv.output.noOutputData.title": "未返回任何输出数据",
  "ndv.output.noOutputData.v2.title": "无输出数据",
  "ndv.output.noOutputData.v2.description": "查看输出数据 {link}",
  "ndv.output.noOutputData.v2.action": "测试此步骤",
  "ndv.output.noOutputData.trigger.title": "无触发器输出",
  "ndv.output.noOutputData.trigger.action": "测试此触发器",
  "ndv.output.noOutputDataInBranch": "此分支中没有输出数据",
  "ndv.output.of": "{current}/{total}",
  "ndv.output.pageSize": "页面大小",
  "ndv.output.run": "运行",
  "ndv.output.runNodeHint": "执行此节点以查看数据",
  "ndv.output.runNodeHintSubNode": "运行父节点后，输出将显示在此处",
  "ndv.output.githubNodeWaitingForWebhook": "在调用以下Webhook URL时，执行将继续：",
  "ndv.output.sendAndWaitWaitingApproval": "执行将在用户响应后继续",
  "ndv.output.waitNodeWaiting.title": "等待输入",
  "ndv.output.waitNodeWaiting.description.webhook": "收到Webhook后继续执行",
  "ndv.output.waitNodeWaiting.description.form": "表单提交后将继续执行",
  "ndv.output.waitNodeWaiting.description.timer": "等待时间结束后将继续执行",
  "ndv.output.insertTestData": "设置模拟数据",
  "ndv.output.staleDataWarning.regular": "节点参数已更改。<br>再次测试节点以刷新输出。",
  "ndv.output.staleDataWarning.pinData": "节点参数更改不会影响固定的输出数据。",
  "ndv.output.tooMuchData.message": "该节点包含 {size} MB 数据。显示它可能会暂时降低浏览器的速度。",
  "ndv.output.tooMuchData.showDataAnyway": "显示数据",
  "ndv.output.tooMuchData.title": "显示数据？",
  "ndv.output.waitingToRun": "正在等待执行...",
  "ndv.output.noToolUsedInfo": "在此运行中未使用任何工具。请尝试为您的工具提供更清晰的名称和描述，以帮助 AI",
  "ndv.title.cancel": "取消",
  "ndv.title.rename": "重命名",
  "ndv.title.rename.placeholder": "输入新名称...",
  "ndv.title.renameNode": "重命名节点",
  "ndv.pinData.pin.title": "固定数据",
  "ndv.pinData.pin.description": "节点将始终输出此数据，而不是执行。",
  "ndv.pinData.pin.binary": "由于此节点的输出包含二进制数据，因此“固定数据”已禁用。",
  "ndv.pinData.pin.link": "更多信息",
  "ndv.pinData.unpin.title": "取消固定数据",
  "ndv.pinData.pin.multipleRuns.title": "运行 #{index} 已固定",
  "ndv.pinData.pin.multipleRuns.description": "每次运行节点时，都会输出此运行。",
  "ndv.pinData.unpinAndExecute.title": "取消固定输出数据？",
  "ndv.pinData.unpinAndExecute.description": "测试节点将覆盖固定的数据。",
  "ndv.pinData.unpinAndExecute.cancel": "取消",
  "ndv.pinData.unpinAndExecute.confirm": "取消固定并测试",
  "ndv.pinData.beforeClosing.title": "在关闭之前保存输出更改？",
  "ndv.pinData.beforeClosing.cancel": "放弃",
  "ndv.pinData.beforeClosing.confirm": "保存",
  "ndv.pinData.error.syntaxError.title": "由于 JSON 无效，无法保存",
  "ndv.pinData.error.tooLarge.title": "由于大小限制，无法固定数据",
  "ndv.pinData.error.tooLarge.description": "工作流已达到允许的最大固定数据大小 ({size} mb / {limit} mb)",
  "ndv.pinData.error.tooLargeWorkflow.title": "由于大小限制，无法固定数据",
  "ndv.pinData.error.tooLargeWorkflow.description": "工作流已达到允许的最大大小 ({size} mb / {limit} mb)",
  "ndv.httpRequest.credentialOnly.docsNotice": '使用<a target="_blank" href="{docsUrl}">{nodeName} 文档</a>来构建您的请求。如果您在下面添加 {nodeName} 凭证，我们将处理身份验证部分。',
  "noTagsView.readyToOrganizeYourWorkflows": "准备好组织您的工作流？",
  "noTagsView.withWorkflowTagsYouReFree": "使用工作流标签，您可以自由地为您的工作流创建完美的标签系统",
  "noAnnotationTagsView.title": "组织您的执行",
  "noAnnotationTagsView.description": "执行标签可帮助您标记和标识不同类别的执行。此外，标记执行后，它永远不会被删除",
  "node.thisIsATriggerNode": '这是一个触发节点。 <a target="_blank" href="https://docs.n8n.io/workflows/components/nodes/">了解更多</a>',
  "node.activateDeactivateNode": "激活/停用节点",
  "node.changeColor": "改变颜色",
  "node.disabled": "已停用",
  "node.testStep": "执行步骤",
  "node.disable": "停用",
  "node.enable": "激活",
  "node.delete": "删除",
  "node.add": "添加",
  "node.issues": "问题",
  "node.dirty": "节点配置已更改。再次运行此节点时，输出数据可能会发生变化。",
  "node.subjectToChange": "由于工作流程的变更，重新运行此节点时输出数据可能会发生变化。",
  "node.nodeIsExecuting": "节点正在执行",
  "node.nodeIsWaitingTill": "节点正在等待，直至 {date} {time}",
  "node.theNodeIsWaitingIndefinitelyForAnIncomingWebhookCall": "节点正在等待传入的 Webhook 调用（无限期）",
  "node.theNodeIsWaitingWebhookCall": "节点正在等待传入的 Webhook 调用",
  "node.theNodeIsWaitingFormCall": "节点正在等待表单提交",
  "node.theNodeIsWaitingUserInput": "节点正在等待用户输入",
  "node.waitingForYouToCreateAnEventIn": "正在等待您在 {nodeType} 中创建事件",
  "node.discovery.pinData.canvas": "您可以固定此输出，而不是等待测试事件。打开节点以执行此操作。",
  "node.discovery.pinData.ndv": "您可以固定此输出，而不是等待测试事件。",
  "node.executionError.openNode": "打开节点",
  "nodeBase.clickToAddNodeOrDragToConnect": "点击以添加节点 \n 或拖动以连接",
  "nodeCreator.actionsPlaceholderNode.scheduleTrigger": "按计划",
  "nodeCreator.actionsPlaceholderNode.webhook": "按 Webhook 调用",
  "nodeCreator.actionsCategory.actions": "操作",
  "nodeCreator.actionsCategory.onNewEvent": "在新 {event} 事件时",
  "nodeCreator.actionsCategory.onEvent": "在 {event} 时",
  "nodeCreator.actionsCategory.triggers": "触发器",
  "nodeCreator.actionsCategory.triggerNodes": "触发节点",
  "nodeCreator.actionsCategory.regularNodes": "常规节点",
  "nodeCreator.actionsCategory.regularAndTriggers": "常规节点和触发节点",
  "nodeCreator.actionsCategory.searchActions": "搜索 {node} 操作...",
  "nodeCreator.actionsCategory.noMatchingActions": "没有匹配的操作。<i>重置搜索</i>",
  "nodeCreator.actionsCategory.noMatchingTriggers": "没有匹配的触发器。<i>重置搜索</i>",
  "nodeCreator.actionsList.apiCall": "没有找到合适的事件？进行<a data-action='addHttpNode'>自定义 {node} API 调用</a>",
  "nodeCreator.actionsCallout.noActionItems": '我们还没有 <strong>{nodeName}</strong> 操作。有想法吗？在我们的 <a target="_blank" href="https://community.n8n.io/c/feature-requests/5">社区中提出请求</a>',
  "nodeCreator.actionsCallout.triggersStartWorkflow": '需要由另一个节点触发操作，例如，使用“<strong>计划</strong>”节点定期触发。<a target="_blank" href="https://docs.n8n.io/integrations/builtin/">了解更多</a>',
  "nodeCreator.actionsTooltip.triggersStartWorkflow": '触发器是启动工作流的步骤。 <a target="_blank" href="https://docs.n8n.io/integrations/builtin/">了解更多</a>',
  "nodeCreator.actionsTooltip.actionsPerformStep": '操作在工作流启动后执行步骤。<a target="_blank" href="https://docs.n8n.io/integrations/builtin/">了解更多</a>',
  "nodeCreator.actionsCallout.noTriggerItems": "没有可用的 <strong>{nodeName}</strong> 触发器。用户通常将以下触发器与 <strong>{nodeName}</strong> 操作组合在一起。",
  "nodeCreator.categoryNames.otherCategories": "其他类别中的结果",
  "nodeCreator.categoryNames.moreFromCommunity": "来自社区的更多内容",
  "nodeCreator.subnodes": "子节点",
  "nodeCreator.noResults.dontWorryYouCanProbablyDoItWithThe": "不用担心，您可能可以使用以下项执行此操作：",
  "nodeCreator.noResults.httpRequest": "HTTP 请求",
  "nodeCreator.noResults.node": "节点",
  "nodeCreator.noResults.or": "或",
  "nodeCreator.noResults.requestTheNode": "请求节点",
  "nodeCreator.noResults.wantUsToMakeItFaster": "希望我们加快速度？",
  "nodeCreator.noResults.weDidntMakeThatYet": "我们还没有制作...尚未制作",
  "nodeCreator.noResults.webhook": "Webhook",
  "nodeCreator.ragStarterTemplate.openTemplateItem.title": "RAG 入门模板",
  "nodeCreator.ragStarterTemplate.openTemplateItem.description": "在 n8n 中了解向量存储",
  "nodeCreator.searchBar.searchNodes": "搜索节点...",
  "nodeCreator.subcategoryDescriptions.appTriggerNodes": "当 Telegram、Notion 或 Airtable 等应用中发生某些事件时运行流",
  "nodeCreator.subcategoryDescriptions.appRegularNodes": "在 Google 表格、Telegram 或 Notion 等应用或服务中执行某些操作",
  "nodeCreator.subcategoryDescriptions.dataTransformation": "操作、筛选或转换数据",
  "nodeCreator.subcategoryDescriptions.files": "CSV、XLS、XML、文本、图像等。",
  "nodeCreator.subcategoryDescriptions.flow": "分支、合并或循环流等。",
  "nodeCreator.subcategoryDescriptions.helpers": "运行代码、进行 HTTP 请求、设置 Webhook 等。",
  "nodeCreator.subcategoryDescriptions.otherTriggerNodes": "在工作流错误、文件更改等情况下运行流。",
  "nodeCreator.subcategoryDescriptions.agents": "交互和做出决策的自主实体。",
  "nodeCreator.subcategoryDescriptions.chains": "用于特定任务的结构化组件。",
  "nodeCreator.subcategoryDescriptions.documentLoaders": "处理文档加载以进行处理。",
  "nodeCreator.subcategoryDescriptions.embeddings": "将文本转换为向量表示形式。",
  "nodeCreator.subcategoryDescriptions.languageModels": "理解和生成语言的 AI 模型。",
  "nodeCreator.subcategoryDescriptions.memory": "管理执行期间信息的存储和检索。",
  "nodeCreator.subcategoryDescriptions.outputParsers": "确保输出符合定义的格式。",
  "nodeCreator.subcategoryDescriptions.retrievers": "从源提取相关信息。",
  "nodeCreator.subcategoryDescriptions.textSplitters": "将文本分解为较小的部分。",
  "nodeCreator.subcategoryDescriptions.tools": "提供各种功能的实用程序组件。",
  "nodeCreator.subcategoryDescriptions.vectorStores": "处理向量表示形式的存储和检索。",
  "nodeCreator.subcategoryDescriptions.miscellaneous": "其他与 AI 相关的节点。",
  "nodeCreator.subcategoryDescriptions.humanInTheLoop": "在继续之前，请等待批准或人工输入",
  "nodeCreator.subcategoryInfos.languageModels": "聊天模型旨在进行交互式对话并很好地遵循说明，而文本补全模型则专注于生成给定文本输入的延续部分",
  "nodeCreator.subcategoryInfos.memory": "内存允许 AI 模型记住并引用过去与它的交互",
  "nodeCreator.subcategoryInfos.vectorStores": "矢量存储允许 AI 模型引用文档的相关部分，这对于问题解答和文档搜索非常有用",
  "nodeCreator.subcategoryNames.appTriggerNodes": "在应用事件时",
  "nodeCreator.subcategoryNames.appRegularNodes": "在应用中执行操作",
  "nodeCreator.subcategoryNames.dataTransformation": "数据转换",
  "nodeCreator.subcategoryNames.files": "文件",
  "nodeCreator.subcategoryNames.flow": "流",
  "nodeCreator.subcategoryNames.helpers": "核心",
  "nodeCreator.subcategoryNames.otherTriggerNodes": "其他方式...",
  "nodeCreator.subcategoryNames.agents": "代理",
  "nodeCreator.subcategoryNames.chains": "链",
  "nodeCreator.subcategoryNames.documentLoaders": "文档加载器",
  "nodeCreator.subcategoryNames.embeddings": "嵌入",
  "nodeCreator.subcategoryNames.languageModels": "语言模型",
  "nodeCreator.subcategoryNames.memory": "内存",
  "nodeCreator.subcategoryNames.outputParsers": "输出解析器",
  "nodeCreator.subcategoryNames.retrievers": "检索器",
  "nodeCreator.subcategoryNames.textSplitters": "文本拆分器",
  "nodeCreator.subcategoryNames.tools": "工具",
  "nodeCreator.subcategoryNames.vectorStores": "矢量存储",
  "nodeCreator.subcategoryNames.miscellaneous": "其他",
  "nodeCreator.subcategoryNames.humanInTheLoop": "人在环路",
  "nodeCreator.sectionNames.popular": "常用",
  "nodeCreator.sectionNames.other": "其他",
  "nodeCreator.sectionNames.sendAndWait": "发送并等待响应",
  "nodeCreator.sectionNames.transform.combine": "组合项目",
  "nodeCreator.sectionNames.transform.addOrRemove": "添加或删除项目",
  "nodeCreator.sectionNames.transform.convert": "转换数据",
  "nodeCreator.triggerHelperPanel.addAnotherTrigger": "添加另一个触发器",
  "nodeCreator.triggerHelperPanel.addAnotherTriggerDescription": "触发器启动您的工作流。工作流可以有多个触发器。",
  "nodeCreator.triggerHelperPanel.title": "此工作流应何时运行？",
  "nodeCreator.triggerHelperPanel.scheduleTriggerDisplayName": "按计划",
  "nodeCreator.triggerHelperPanel.scheduleTriggerDescription": "每天、每小时或自定义间隔运行流",
  "nodeCreator.triggerHelperPanel.webhookTriggerDisplayName": "在 Webhook 调用时",
  "nodeCreator.triggerHelperPanel.webhookTriggerDescription": "在收到 HTTP 请求时运行流",
  "nodeCreator.triggerHelperPanel.formTriggerDisplayName": "在表单提交时",
  "nodeCreator.triggerHelperPanel.formTriggerDescription": "在 n8n 中生成 Web 表单并将它们的响应传递到工作流",
  "nodeCreator.triggerHelperPanel.formDisplayName": "表单",
  "nodeCreator.triggerHelperPanel.formDescription": "添加下一张表单页",
  "nodeCreator.triggerHelperPanel.manualTriggerDisplayName": "手动触发",
  "nodeCreator.triggerHelperPanel.manualTriggerDescription": "在 n8n 中点击按钮时运行流。适合快速入门",
  "nodeCreator.triggerHelperPanel.manualChatTriggerDisplayName": "在聊天消息时",
  "nodeCreator.triggerHelperPanel.manualChatTriggerDescription": "当用户发送聊天消息时运行流。用于 AI 节点",
  "nodeCreator.triggerHelperPanel.manualTriggerTag": "推荐",
  "nodeCreator.triggerHelperPanel.chatTriggerDisplayName": "在聊天消息时",
  "nodeCreator.triggerHelperPanel.chatTriggerDescription": "当用户发送聊天消息时运行流。用于 AI 节点",
  "nodeCreator.triggerHelperPanel.whatHappensNext": "接下来会发生什么？",
  "nodeCreator.triggerHelperPanel.selectATrigger": "什么触发此工作流？",
  "nodeCreator.triggerHelperPanel.selectATriggerDescription": "触发器是启动工作流的步骤",
  "nodeCreator.triggerHelperPanel.workflowTriggerDisplayName": "由另一个工作流执行时",
  "nodeCreator.triggerHelperPanel.workflowTriggerDescription": "当由不同工作流的“执行工作流”节点调用时，运行流",
  "nodeCreator.aiPanel.aiNodes": "AI 节点",
  "nodeCreator.aiPanel.aiOtherNodes": "其他 AI 节点",
  "nodeCreator.aiPanel.aiOtherNodesDescription": "嵌入、矢量存储、LLM 和其他 AI 节点",
  "nodeCreator.aiPanel.selectAiNode": "选择一个人工智能节点添加到您的工作流程中",
  "nodeCreator.aiPanel.nodesForAi": "构建自主代理，总结或搜索文档等。",
  "nodeCreator.aiPanel.newTag": "新建",
  "nodeCreator.aiPanel.langchainAiNodes": "人工智能",
  "nodeCreator.aiPanel.title": "此工作流应何时运行？",
  "nodeCreator.aiPanel.linkItem.description": "了解可以实现的功能并以 5 倍的速度开始使用",
  "nodeCreator.aiPanel.linkItem.title": "AI 模板",
  "nodeCreator.aiPanel.scheduleTriggerDisplayName": "按计划",
  "nodeCreator.aiPanel.scheduleTriggerDescription": "每天、每小时或自定义间隔运行流",
  "nodeCreator.aiPanel.webhookTriggerDisplayName": "在 Webhook 调用时",
  "nodeCreator.aiPanel.webhookTriggerDescription": "当另一个应用发送 Webhook 时运行流",
  "nodeCreator.aiPanel.manualTriggerDisplayName": "手动",
  "nodeCreator.aiPanel.manualTriggerDescription": "在点击 n8n 中的按钮时运行流",
  "nodeCreator.aiPanel.whatHappensNext": "接下来会发生什么？",
  "nodeCreator.aiPanel.selectATrigger": "选择 AI 组件",
  "nodeCreator.aiPanel.selectATriggerDescription": "触发器是启动工作流的步骤",
  "nodeCreator.aiPanel.workflowTriggerDisplayName": "当被另一个工作流调用时",
  "nodeCreator.aiPanel.workflowTriggerDescription": "当由不同工作流的“执行工作流”节点调用时，运行流",
  "nodeCreator.nodeItem.triggerIconTitle": "触发节点",
  "nodeCreator.nodeItem.aiIconTitle": "LangChain AI 节点",
  "nodeCreator.nodeItem.deprecated": "已弃用",
  "nodeCredentials.createNew": "创建新凭证",
  "nodeCredentials.credentialFor": "{credentialType} 的凭证",
  "nodeCredentials.credentialsLabel": "要连接的凭证",
  "nodeCredentials.issues": "问题",
  "nodeCredentials.selectCredential": "选择凭证",
  "nodeCredentials.selectedCredentialUnavailable": "{name}（不可用）",
  "nodeCredentials.showMessage.message": "使用凭证“{oldCredentialName}”的节点已更新为使用“{newCredentialName}”",
  "nodeCredentials.showMessage.title": "节点凭证已更新",
  "nodeCredentials.updateCredential": "更新凭证",
  "nodeErrorView.cause": "原因",
  "nodeErrorView.copyToClipboard": "复制到剪贴板",
  "nodeErrorView.copyToClipboard.tooltip": "复制错误详细信息以进行调试。复制的数据可能包含敏感信息。共享时请谨慎。",
  "nodeErrorView.dataBelowMayContain": "以下数据可能包含敏感信息。共享时请谨慎。",
  "nodeErrorView.details": "详细信息",
  "nodeErrorView.details.from": "来自 {node}",
  "nodeErrorView.details.rawMessages": "完整消息",
  "nodeErrorView.details.errorData": "错误数据",
  "nodeErrorView.details.errorExtra": "错误额外信息",
  "nodeErrorView.details.request": "请求",
  "nodeErrorView.details.title": "错误详细信息",
  "nodeErrorView.details.message": "错误消息",
  "nodeErrorView.details.info": "其他信息",
  "nodeErrorView.details.nodeVersion": "节点版本",
  "nodeErrorView.details.nodeType": "节点类型",
  "nodeErrorView.details.n8nVersion": "n8n 版本",
  "nodeErrorView.details.errorCause": "错误原因",
  "nodeErrorView.details.causeDetailed": "详细原因",
  "nodeErrorView.details.stackTrace": "堆栈跟踪",
  "nodeErrorView.error": "错误",
  "nodeErrorView.errorSubNode": "子节点“{node}”中出错",
  "nodeErrorView.httpCode": "HTTP 代码",
  "nodeErrorView.errorCode": "错误代码",
  "nodeErrorView.inParameter": "在或在参数下",
  "nodeErrorView.itemIndex": "项目索引",
  "nodeErrorView.runIndex": "运行索引",
  "nodeErrorView.showMessage.title": "已复制到剪贴板",
  "nodeErrorView.stack": "堆栈",
  "nodeErrorView.theErrorCauseIsTooLargeToBeDisplayed": "错误原因太大，无法显示",
  "nodeErrorView.time": "时间",
  "nodeErrorView.inputPanel.previousNodeError.title": "运行节点“{nodeName}”时出错",
  "nodeErrorView.description.pairedItemInvalidInfo": '此处的表达式将不起作用，因为它使用 <code>.item</code> 且 n8n 无法确定<a target="_blank" href="https://docs.n8n.io/data/data-mapping/data-item-linking/item-linking-errors/">匹配项</a>。这是因为节点 <strong>“{nodeCause}”</strong> 返回了不正确的匹配信息（对于运行 {runIndex} 的项 {itemIndex}）。<br/><br/>请尝试使用 <code>.first()</code>、<code>.last()</code> 或 <code>.all()[index]</code>，而不是 <code>.item</code>。',
  "nodeErrorView.description.pairedItemNoInfo": '此处的表达式将不起作用，因为它使用 <code>.item</code> 且 n8n 无法确定<a target="_blank" href="https://docs.n8n.io/data/data-mapping/data-item-linking/item-linking-errors/">匹配项</a>。节点 <strong>“{nodeCause}”</strong> 没有返回足够的信息。',
  "nodeErrorView.description.pairedItemNoInfoCodeNode": '此处的表达式将不起作用，因为它使用 <code>.item</code> 且 n8n 无法确定<a target="_blank" href="https://docs.n8n.io/data/data-mapping/data-item-linking/item-linking-errors/">匹配项</a>。您可以：<ul><li>将<a target="_blank" href="https://docs.n8n.io/data/data-mapping/data-item-linking/item-linking-code-node/">缺失信息</a>添加到节点 <strong>“{nodeCause}”</strong></li><li>或者使用 <code>.first()</code>、<code>.last()</code> 或 <code>.all()[index]</code>，而不是 <code>.item</code></li></ul>',
  "nodeErrorView.description.pairedItemNoConnection": "没有回到节点 <strong>“{nodeCause}”</strong> 的连接，但此处的一个表达式中使用了该节点。<br/><br/>请连接节点（中间可以有其他节点）。",
  "nodeErrorView.description.pairedItemNoConnectionCodeNode": "没有回到节点 <strong>“{nodeCause}”</strong> 的连接，但此处的一个代码中使用了该节点。<br/><br/>请连接节点（中间可以有其他节点）。",
  "nodeErrorView.description.noNodeExecutionData": "一个表达式引用了节点 <strong>“{nodeCause}”</strong>，但尚未执行。更改表达式，或重新连接您的工作流以确保先执行该节点。",
  "nodeErrorView.description.nodeNotFound": "节点 <strong>“{nodeCause}”</strong> 不存在，但此处的一个表达式中使用了该节点。",
  "nodeErrorView.description.noInputConnection": "此节点没有输入数据。请确保此节点已连接到另一个节点。",
  "nodeErrorView.description.pairedItemMultipleMatches": '此处的表达式将不起作用，因为它使用 <code>.item</code> 且 n8n 无法确定<a target="_blank" href="https://docs.n8n.io/data/data-mapping/data-item-linking/item-linking-errors/">匹配项</a>。（有多个可能的匹配项）<br/><br/>请尝试使用 <code>.first()</code>、<code>.last()</code> 或 <code>.all()[index]</code>，而不是 <code>.item</code>，或<a target="_blank" href="https://docs.n8n.io/data/data-mapping/data-item-linking/item-linking-code-node/">引用另一个节点</a>。',
  "nodeErrorView.description.pairedItemMultipleMatchesCodeNode": '这里的代码将不起作用，因为它使用 <code>.item</code> 且 n8n 无法确定<a target="_blank" href="https://docs.n8n.io/data/data-mapping/data-item-linking/item-linking-errors/">匹配项</a>。（有多个可能的匹配项）<br/><br/>请尝试使用 <code>.first()</code>、<code>.last()</code> 或 <code>.all()[index]</code>，而不是 <code>.item</code>，或<a target="_blank" href="https://docs.n8n.io/data/data-mapping/data-item-linking/item-linking-code-node/">引用另一个节点</a>。',
  "nodeErrorView.description.pairedItemPinned": '该节点中的<a target="_blank" href="https://docs.n8n.io/data/data-mapping/data-item-linking/item-linking-errors/">项匹配</a>数据可能已过时。此节点中使用 <code>.item</code> 的表达式需要此信息。',
  "nodeHelpers.credentialsUnset": "未设置“{credentialType}”的凭证。",
  "nodeSettings.alwaysOutputData.description": "如果处于活动状态，则当输出为空时，将输出一个空的单个项。用于防止工作流在此节点上完成。",
  "nodeSettings.alwaysOutputData.displayName": "始终输出数据",
  "nodeSettings.clickOnTheQuestionMarkIcon": "点击“？”图标以在 n8n.io 上打开此节点",
  "nodeSettings.onError.description": "当节点执行失败时要采取的操作",
  "nodeSettings.onError.displayName": "出现错误时",
  "nodeSettings.onError.options.continueRegularOutput.description": "将错误消息作为常规输出中的项传递",
  "nodeSettings.onError.options.continueRegularOutput.displayName": "继续",
  "nodeSettings.onError.options.continueErrorOutput.description": "将项传递到额外的 error 输出",
  "nodeSettings.onError.options.continueErrorOutput.displayName": "继续（使用错误输出）",
  "nodeSettings.onError.options.stopWorkflow.description": "停止执行并使工作流失败",
  "nodeSettings.onError.options.stopWorkflow.displayName": "停止工作流",
  "nodeSettings.docs": "文档",
  "nodeSettings.executeButtonTooltip.times": "将执行 {inputSize} 次，每个输入项执行一次",
  "nodeSettings.executeOnce.description": "如果处于活动状态，则节点仅执行一次，使用接收到的第一个项的数据",
  "nodeSettings.executeOnce.displayName": "执行一次",
  "nodeSettings.maxTries.description": "在执行失败之前尝试执行节点的次数",
  "nodeSettings.maxTries.displayName": "最大尝试次数",
  "nodeSettings.noDescriptionFound": "未找到描述",
  "nodeSettings.nodeDescription": "节点描述",
  "nodeSettings.notes.description": "要与节点一起保存的可选注释",
  "nodeSettings.notes.displayName": "注释",
  "nodeSettings.notesInFlow.description": "如果处于活动状态，则上述注释将作为副标题显示在流中",
  "nodeSettings.notesInFlow.displayName": "在流中显示注释？",
  "nodeSettings.parameters": "参数",
  "nodeSettings.settings": "设置",
  "nodeSettings.communityNodeTooltip": '这是一个 <a href="{docUrl}" target="_blank"/>社区节点</a>',
  "nodeSettings.retryOnFail.description": "如果处于活动状态，则节点在失败时尝试再次执行",
  "nodeSettings.retryOnFail.displayName": "失败时重试",
  "nodeSettings.scopes.expandedNoticeWithScopes": '<a data-key="toggle-expand">{count} 个范围</a>可用于 {activeCredential} 凭证<br>{scopes}<br><a data-key="show-less">显示较少</a> | <a data-key="toggle-expand">{count} 个范围</a>可用于 {activeCredential} 凭证<br>{scopes}<br><a data-key="show-less">显示较少</a>',
  "nodeSettings.scopes.notice": '<a data-key="toggle-expand">{count} 个范围</a>可用于 {activeCredential} 凭证 | <a data-key="toggle-expand">{count} 个范围</a>可用于 {activeCredential} 凭证',
  "nodeSettings.theNodeIsNotValidAsItsTypeIsUnknown": "节点无效，因为其类型 ({nodeType}) 未知",
  "nodeSettings.communityNodeDetails.title": "节点详情",
  "nodeSettings.communityNodeUnknown.title": "安装此节点以使用它",
  "nodeSettings.communityNodeUnknown.description": "此节点当前未安装。它是 {action} 社区包的一部分。",
  "nodeSettings.communityNodeUnknown.installLink.text": "如何安装社区节点",
  "nodeSettings.nodeTypeUnknown.description": "此节点当前未安装。它要么来自较新版本的 n8n、一个 {action}，要么具有无效的结构",
  "nodeSettings.nodeTypeUnknown.description.customNode": "自定义节点",
  "nodeSettings.thisNodeDoesNotHaveAnyParameters": "此节点没有任何参数",
  "nodeSettings.useTheHttpRequestNode": '使用 <b>HTTP 请求</b>节点进行自定义 API 调用。我们将为您处理 {nodeTypeDisplayName} 身份验证。<a target="_blank" href="https://docs.n8n.io/integrations/custom-operations/">了解更多</a>',
  "nodeSettings.waitBetweenTries.description": "每次尝试之间等待的时间（以毫秒为单位）",
  "nodeSettings.waitBetweenTries.displayName": "等待尝试之间的时间（毫秒）",
  "nodeSettings.hasForeignCredential": "要编辑此节点，请执行以下操作之一：<br/>a) 请 {owner} 与您共享凭证，或<br/>b) 复制节点并添加您自己的凭证",
  "nodeSettings.latest": "最新",
  "nodeSettings.deprecated": "已弃用",
  "nodeSettings.latestVersion": "最新版本：{version}",
  "nodeSettings.outputCleared.title": "参数已更改",
  "nodeSettings.outputCleared.message": "参数顺序已更改，传出连接已清除",
  "nodeSettings.nodeVersion": "{node} 节点版本 {version}",
  "nodeView.addNode": "添加节点",
  "nodeView.openFocusPanel": "打开焦点面板",
  "nodeView.openNodesPanel": "打开节点面板",
  "nodeView.addATriggerNodeFirst": "首先添加<a data-action='showNodeCreator'>触发节点</a>",
  "nodeView.addOrEnableTriggerNode": "<a data-action='showNodeCreator'>添加</a>或启用触发节点以执行工作流",
  "nodeView.addSticky": "点击以添加便签",
  "nodeView.addStickyHint": "添加便签",
  "nodeView.cantExecuteNoTrigger": "无法执行工作流",
  "nodeView.canvasAddButton.addATriggerNodeBeforeExecuting": "在执行工作流之前添加触发节点",
  "nodeView.canvasAddButton.addFirstStep": "添加第一步...",
  "nodeView.templateLink": "或者从模板开始",
  "nodeView.confirmMessage.onClipboardPasteEvent.cancelButtonText": "",
  "nodeView.confirmMessage.onClipboardPasteEvent.confirmButtonText": "是，导入",
  "nodeView.confirmMessage.onClipboardPasteEvent.headline": "导入工作流？",
  "nodeView.confirmMessage.onClipboardPasteEvent.message": "将从<br /><i>{plainTextData}<i>导入工作流",
  "nodeView.confirmMessage.debug.cancelButtonText": "取消",
  "nodeView.confirmMessage.debug.confirmButtonText": "取消固定",
  "nodeView.confirmMessage.debug.headline": "取消固定工作流数据",
  "nodeView.confirmMessage.debug.message": "加载此执行将取消固定当前在这些节点中固定的数据",
  "nodeView.couldntImportWorkflow": "无法导入工作流",
  "nodeView.couldntLoadWorkflow.invalidWorkflowObject": "无效的工作流对象",
  "nodeView.deletesTheCurrentExecutionData": "删除当前执行数据",
  "nodeView.focusPanel.noParameters.title": "在这里显示一个节点参数，以便于迭代。",
  "nodeView.focusPanel.noParameters.subtitle": "例如，始终保持提示可见，以便在调整工作流时可以运行它。",
  "nodeView.focusPanel.missingParameter": "该参数不再在节点上可见。可能修改了相关的参数，从而移除了这个参数。",
  "nodeView.itLooksLikeYouHaveBeenEditingSomething": "您似乎进行了一些编辑。如果您在保存前离开，您的更改将会丢失。",
  "nodeView.loadingTemplate": "正在加载模板",
  "nodeView.moreInfo": "更多信息",
  "nodeView.noNodesGivenToAdd": "未指定要添加的节点",
  "nodeView.prompt.cancel": "@:_reusableBaseText.cancel",
  "nodeView.prompt.invalidName": "无效的名称",
  "nodeView.prompt.newName": "新名称",
  "nodeView.prompt.rename": "重命名",
  "nodeView.prompt.renameNode": "重命名节点",
  "nodeView.redirecting": "正在重定向",
  "nodeView.refresh": "刷新",
  "nodeView.resetZoom": "重置缩放",
  "nodeView.tidyUp": "整理",
  "nodeView.runButtonText.executeWorkflow": "执行工作流程",
  "nodeView.runButtonText.executingWorkflow": "正在执行工作流",
  "nodeView.runButtonText.waitingForTriggerEvent": "正在等待触发事件",
  "nodeView.runButtonText.from": "来自 {nodeName}",
  "nodeView.showError.workflowError": "工作流执行时发生错误",
  "nodeView.showError.getWorkflowDataFromUrl.title": "加载工作流时出现问题",
  "nodeView.showError.importWorkflowData.title": "导入工作流时出现问题",
  "nodeView.showError.mounted1.message": "加载初始数据时出现问题",
  "nodeView.showError.mounted1.title": "初始化问题",
  "nodeView.showError.mounted2.message": "初始化工作流时出现问题",
  "nodeView.showError.mounted2.title": "初始化问题",
  "nodeView.showError.openExecution.title": "加载执行时出现问题",
  "nodeView.showError.openExecution.node": "执行过程中打开节点时出现问题",
  "nodeView.showError.openWorkflow.title": "打开工作流时出现问题",
  "nodeView.showError.stopExecution.title": "停止执行时出现问题",
  "nodeView.showError.stopWaitingForWebhook.title": "删除测试 Webhook 时出现问题",
  "nodeView.showError.nodeNodeCompatible.title": "无法连接",
  "nodeView.showError.nodeNodeCompatible.message": "节点“{sourceNodeName}”无法连接到节点“{targetNodeName}”，因为它们不兼容。",
  "nodeView.showMessage.addNodeButton.message": "“{nodeTypeName}”是未知的节点类型",
  "nodeView.showMessage.addNodeButton.title": "无法插入节点",
  "nodeView.showMessage.keyDown.title": "工作流已创建",
  "nodeView.showMessage.showMaxNodeTypeError.message": "一个工作流中只允许一个“{nodeTypeDataDisplayName}”节点 | 一个工作流中只允许 {count} 个“{nodeTypeDataDisplayName}”节点",
  "nodeView.showMessage.showMaxNodeTypeError.title": "无法插入节点",
  "nodeView.showMessage.stopExecutionCatch.unsaved.message": "此执行已取消",
  "nodeView.showMessage.stopExecutionCatch.unsaved.title": "执行已取消",
  "nodeView.showMessage.stopExecutionCatch.message": "它在可以停止之前就已完成",
  "nodeView.showMessage.stopExecutionCatch.title": "工作流已完成执行",
  "nodeView.showMessage.stopExecutionTry.title": "执行已停止",
  "nodeView.showMessage.debug.title": "已导入执行数据",
  "nodeView.showMessage.debug.content": "您可以进行编辑并重新执行。完成后，取消固定第一个节点。",
  "nodeView.showMessage.debug.missingNodes.title": "未导入某些执行数据",
  "nodeView.showMessage.debug.missingNodes.content": "自执行运行以来，某些节点已被删除或重命名或添加到工作流中。",
  "nodeView.showMessage.ndvUrl.missingNodes.title": "节点未找到",
  "nodeView.showMessage.ndvUrl.missingNodes.content": "URL 中包含了对未知节点的引用。可能是该节点已被删除？",
  "nodeView.stopCurrentExecution": "停止当前执行",
  "nodeView.stopWaitingForWebhookCall": "停止等待 Webhook 调用",
  "nodeView.stoppingCurrentExecution": "正在停止当前执行",
  "nodeView.thereWasAProblemLoadingTheNodeParametersOfNode": "加载节点参数时出现问题",
  "nodeView.thisExecutionHasntFinishedYet": "此执行尚未完成",
  "nodeView.toSeeTheLatestStatus": "查看最新状态",
  "nodeView.workflowTemplateWithIdCouldNotBeFound": "找不到 ID 为“{templateId}”的工作流模板",
  "nodeView.workflowWithIdCouldNotBeFound": "找不到 ID 为“{workflowId}”的工作流",
  "nodeView.zoomIn": "放大",
  "nodeView.zoomOut": "缩小",
  "nodeView.zoomToFit": "缩放到适合",
  "nodeView.replaceMe": "替换我",
  "nodeView.setupTemplate": "设置模板",
  "nodeView.expandAllNodes": "展开所有节点",
  "nodeView.collapseAllNodes": "折叠所有节点",
  "nodeViewV2.showError.editingNotAllowed": "不允许编辑",
  "nodeViewV2.showError.failedToCreateNode": "创建节点失败",
  "contextMenu.node": "节点 | 节点",
  "contextMenu.sticky": "便签 | 便签",
  "contextMenu.selectAll": "全选",
  "contextMenu.deselectAll": "清除选择",
  "contextMenu.tidyUpWorkflow": "整理工作流程",
  "contextMenu.tidyUpSelection": "整理所选内容",
  "contextMenu.extract": "将节点转换为子工作流 | 将 {count} 个节点转换为子工作流",
  "contextMenu.duplicate": "复制 | 复制 {count} 个 {subject}",
  "contextMenu.open": "打开...",
  "contextMenu.test": "测试步骤",
  "contextMenu.rename": "重命名",
  "contextMenu.openSubworkflow": "转到子工作流程",
  "contextMenu.copy": "复制 | 复制 {count} 个 {subject}",
  "contextMenu.deactivate": "停用 | 停用 {count} 个 {subject}",
  "contextMenu.activate": "激活 | 激活 {count} 个节点",
  "contextMenu.pin": "固定 | 固定 {count} 个节点",
  "contextMenu.unpin": "取消固定 | 取消固定 {count} 个节点",
  "contextMenu.delete": "删除 | 删除 {count} 个 {subject}",
  "contextMenu.addNode": "添加节点",
  "contextMenu.addSticky": "添加便签",
  "contextMenu.editSticky": "编辑便签",
  "contextMenu.changeColor": "更改颜色",
  "nodeWebhooks.clickToCopyWebhookUrls": "点击复制 Webhook URL",
  "nodeWebhooks.clickToCopyWebhookUrls.formTrigger": "点击复制表单 URL",
  "nodeWebhooks.clickToCopyWebhookUrls.chatTrigger": "点击复制聊天 URL",
  "nodeWebhooks.clickToCopyWebhookUrls.mcpTrigger": "点击复制MCP网址",
  "nodeWebhooks.clickToDisplayWebhookUrls": "点击显示 Webhook URL",
  "nodeWebhooks.clickToDisplayWebhookUrls.formTrigger": "点击显示表单 URL",
  "nodeWebhooks.clickToDisplayWebhookUrls.chatTrigger": "点击显示聊天 URL",
  "nodeWebhooks.clickToDisplayWebhookUrls.mcpTrigger": "点击显示MCP网址",
  "nodeWebhooks.clickToHideWebhookUrls": "点击隐藏 Webhook URL",
  "nodeWebhooks.clickToHideWebhookUrls.formTrigger": "点击隐藏表单 URL",
  "nodeWebhooks.clickToHideWebhookUrls.chatTrigger": "点击隐藏聊天 URL",
  "nodeWebhooks.clickToHideWebhookUrls.mcpTrigger": "点击隐藏MCP网址",
  "nodeWebhooks.invalidExpression": "[无效的表达式]",
  "nodeWebhooks.productionUrl": "生产 URL",
  "nodeWebhooks.showMessage.title": "URL 已复制",
  "nodeWebhooks.showMessage.title.formTrigger": "表单 URL 已复制",
  "nodeWebhooks.showMessage.title.chatTrigger": "聊天 URL 已复制",
  "nodeWebhooks.showMessage.title.mcpTrigger": "MCP URL已复制",
  "nodeWebhooks.showMessage.message.formTrigger": "通过此 URL 提交的表单将在激活时触发工作流",
  "nodeWebhooks.showMessage.message.chatTrigger": "通过此 URL 提交的聊天将在激活时触发工作流",
  "nodeWebhooks.testUrl": "测试 URL",
  "nodeWebhooks.webhookUrls": "Webhook URL",
  "nodeWebhooks.webhookUrls.formTrigger": "表单 URL",
  "nodeWebhooks.webhookUrls.chatTrigger": "聊天 URL",
  "nodeWebhooks.webhookUrls.mcpTrigger": "MCP 网址",
  "openWorkflow.workflowImportError": "无法导入工作流",
  "openWorkflow.workflowNotFoundError": "找不到工作流",
  "parameterInput.expressionResult": "例如 {result}",
  "parameterInput.tip": "提示",
  "parameterInput.dragTipBeforePill": "拖动",
  "parameterInput.inputField": "输入字段",
  "parameterInput.dragTipAfterPill": "从左侧拖动到此处使用。",
  "parameterInput.learnMore": "了解更多",
  "parameterInput.result": "结果",
  "parameterInput.item": "项目",
  "parameterInput.hoverTableItemTip": "您还可以通过将鼠标悬停在表视图中的输入/输出项上来执行此操作",
  "parameterInput.emptyString": "[空]",
  "parameterInput.customApiCall": "自定义 API 调用",
  "parameterInput.error": "错误",
  "parameterInput.expression": "表达式",
  "parameterInput.fixed": "固定值",
  "parameterInput.formatHtml": "格式化 HTML",
  "parameterInput.issues": "问题",
  "parameterInput.loadingOptions": "正在加载选项...",
  "parameterInput.loadOptionsErrorService": "从 {service} 获取选项时出错",
  "parameterInput.loadOptionsError": "获取选项时出错",
  "parameterInput.loadOptionsCredentialsRequired": "设置凭证以查看选项",
  "parameterInput.openEditWindow": "打开编辑窗口",
  "parameterInput.parameter": "参数：“{shortPath}”",
  "parameterInput.parameterHasExpression": "参数：“{shortPath}”有一个表达式",
  "parameterInput.parameterHasIssues": "参数：“{shortPath}”有问题",
  "parameterInput.parameterHasIssuesAndExpression": "参数：“{shortPath}”有问题并且有一个表达式",
  "parameterInput.refreshList": "刷新列表",
  "parameterInput.clearContents": "清除内容",
  "parameterInput.focusParameter": "焦点参数",
  "parameterInput.resetValue": "重置值",
  "parameterInput.select": "选择",
  "parameterInput.selectDateAndTime": "选择日期和时间",
  "parameterInput.selectACredentialTypeFromTheDropdown": "从下拉列表中选择凭证类型",
  "parameterInput.theValueIsNotSupported": "不支持值“{checkValue}”！",
  "parameterInput.selectedWorkflowIsArchived": "所选工作流程已存档",
  "parameterInputExpanded.openDocs": "打开文档",
  "parameterInputExpanded.thisFieldIsRequired": "此字段为必填字段",
  "parameterInputList.delete": "删除",
  "parameterInputList.deleteParameter": "删除参数",
  "parameterInputList.parameterOptions": "参数选项",
  "parameterInputList.loadingFields": "正在加载字段...",
  "parameterInputList.loadingError": "加载字段时出错。请刷新页面并重试。",
  "parameterInputList.callout.dismiss.confirm.text": "你想要永久隐藏此项吗？",
  "parameterInputList.callout.dismiss.confirm.confirmButtonText": "确认",
  "parameterInputList.callout.dismiss.confirm.cancelButtonText": "取消",
  "parameterOverride.overridePanelText": "由<b>模型</b>自动定义",
  "parameterOverride.applyOverrideButtonTooltip": "让模型来定义该参数",
  "parameterOverride.descriptionTooltip": "向大语言模型（LLM）说明应如何生成该数值。一个良好且具体的描述能使LLM更频繁地生成预期结果。",
  "personalizationModal.businessOwner": "企业主",
  "personalizationModal.continue": "继续",
  "personalizationModal.cicd": "CI/CD",
  "personalizationModal.cloudInfrastructureOrchestration": "云基础设施编排",
  "personalizationModal.customerIntegrations": "客户集成",
  "personalizationModal.customerSupport": "客户支持",
  "personalizationModal.customizeN8n": "为您定制 n8n",
  "personalizationModal.dataScience": "数据科学",
  "personalizationModal.devops": "Devops",
  "personalizationModal.digitalAgencyOrConsultant": "营销机构/咨询",
  "personalizationModal.eCommerce": "电子商务",
  "personalizationModal.education": "教育",
  "personalizationModal.engineering": "工程",
  "personalizationModal.engineeringOrDevops": "工程/Devops",
  "personalizationModal.errorWhileSubmittingResults": "提交结果时出错",
  "personalizationModal.financeOrAccounting": "财务/会计",
  "personalizationModal.financeOrInsurance": "财务/保险",
  "personalizationModal.getStarted": "开始",
  "personalizationModal.government": "政府",
  "personalizationModal.healthcare": "医疗保健",
  "personalizationModal.howAreYourCodingSkills": "您的编码技能如何？",
  "personalizationModal.howBigIsYourCompany": "您的公司规模有多大？",
  "personalizationModal.hr": "人力资源",
  "personalizationModal.imNotUsingN8nForWork": "我没有将 n8n 用于工作",
  "personalizationModal.it": "IT",
  "personalizationModal.legal": "法律",
  "personalizationModal.lessThan20People": "少于 20 人",
  "personalizationModal.managedServiceProvider": "托管服务提供商",
  "personalizationModal.manufacturing": "制造业",
  "personalizationModal.marketing": "市场营销",
  "personalizationModal.media": "媒体",
  "personalizationModal.notSureYet": "尚不确定",
  "personalizationModal.operations": "运营",
  "personalizationModal.other": "其他",
  "personalizationModal.otherPleaseSpecify": "其他（请指定）",
  "personalizationModal.specifyReportedSource": "指定您如何了解 n8n",
  "personalizationModal.people": "人",
  "personalizationModal.physicalRetailOrServices": "实体零售或服务",
  "personalizationModal.product": "产品（例如，快速原型设计）",
  "personalizationModal.realEstateOrConstruction": "房地产/建筑",
  "personalizationModal.saas": "软件即服务",
  "personalizationModal.salesAndMarketing": "销售和营销",
  "personalizationModal.security": "安全",
  "personalizationModal.select": "选择...",
  "personalizationModal.howDidYouHearAboutN8n": "您是如何知道 n8n 的？",
  "personalizationModal.friendWordOfMouth": "朋友/口头传播",
  "personalizationModal.podcast": "播客",
  "personalizationModal.event": "活动",
  "personalizationModal.myself": "我自己",
  "personalizationModal.myTeam": "我的团队",
  "personalizationModal.otherTeams": "其他团队",
  "personalizationModal.specifyAutomationBeneficiary": "您的自动化主要针对哪些人？",
  "personalizationModal.specifyYourRole": "请指定您的角色",
  "personalizationModal.specifyYourAutomationGoal": "请指定您的自动化目标",
  "personalizationModal.specifyYourCompanysIndustry": "指定您公司所在的行业",
  "personalizationModal.support": "支持",
  "personalizationModal.systemsIntegrator": "系统集成商/自动化机构",
  "personalizationModal.telecoms": "电信",
  "personalizationModal.thanks": "谢谢！",
  "personalizationModal.theseQuestionsHelpUs": "这些问题有助于我们为您量身定制 n8n",
  "personalizationModal.whichRoleBestDescribesYou": "哪个角色最能描述您？",
  "personalizationModal.whatAreYouLookingToAutomate": "您希望自动化什么？",
  "personalizationModal.whatBestDescribesYourCompany": "什么最能描述您的公司？",
  "personalizationModal.whichIndustriesIsYourCompanyIn": "您的公司在哪些行业？",
  "personalizationModal.specifySalesMarketingGoal": "销售和营销的哪些部分？",
  "personalizationModal.leadGeneration": "潜在客户开发、扩充、路由",
  "personalizationModal.customerCommunication": "客户沟通",
  "personalizationModal.customerActions": "潜在客户状态发生更改时采取的操作",
  "personalizationModal.yourEmailAddress": "您的电子邮件地址",
  "personalizationModal.email": "输入您的电子邮件..",
  "personalizationModal.adCampaign": "广告系列管理",
  "personalizationModal.reporting": "报告",
  "personalizationModal.ticketingSystemsIntegrations": "票务系统集成",
  "personalizationModal.dataSynching": "数据同步",
  "personalizationModal.incidentResponse": "事件响应",
  "personalizationModal.monitoringAndAlerting": "监控和警报",
  "personalizationModal.specifyUsageMode": "您是否正在考虑执行以下任何操作？",
  "personalizationModal.connectToInternalDB": "连接到我公司的内部数据库",
  "personalizationModal.buildBackendServices": "构建后端服务（端点）",
  "personalizationModal.manipulateFiles": "操作/传输文件",
  "personalizationModal.specifyOtherSalesAndMarketingGoal": "指定您其他的销售和营销目标",
  "personalizationModal.registerEmailForTrial": "注册您的电子邮件以激活我们 {trial} 的 14 天免费试用版",
  "personalizationModal.registerEmailForTrial.enterprise": "企业版功能",
  "personalizationModal.registerEmailForTrial.notice": "通过选中此框，您同意允许我们存储您的姓名和电子邮件以激活您的试用版并发送您的许可证密钥。我们将在试用结束时进行检查，以确保您充分利用我们的企业版功能。",
  "personalizationModal.registerEmailForTrial.success.title": "您的企业版许可证正在发送中",
  "personalizationModal.registerEmailForTrial.success.message": "您将很快收到一封电子邮件以激活您的企业版许可证。如果您没有看到它，请检查您的垃圾邮件文件夹。",
  "personalizationModal.registerEmailForTrial.success.button": "开始使用 n8n",
  "personalizationModal.registerEmailForTrial.error": "注册企业版试用时出错",
  "pushConnection.nodeExecutedSuccessfully": "节点已成功执行",
  "pushConnection.workflowExecutedSuccessfully": "工作流已成功执行",
  "pushConnectionTracker.cannotConnectToServer": "您有连接问题或服务器已关闭。<br />一旦问题得到解决，n8n 应会自动重新连接。",
  "pushConnectionTracker.connectionLost": "连接已丢失",
  "pushConnectionTracker.connectionLost.message": "正在尝试重新连接...",
  "pushConnection.pollingNode.dataNotFound": "未找到 {service} 数据",
  "pushConnection.pollingNode.dataNotFound.message": "我们在 {service} 中没有找到任何数据来模拟事件。请在 {service} 中创建一个，然后重试。",
  "pushConnection.executionFailed": "执行失败",
  "pushConnection.executionFailed.message": '可能没有足够的内存来完成执行。此处提供了有关避免此问题的提示 <a target="_blank" href="https://docs.n8n.io/flow-logic/error-handling/memory-errors/">here</a>',
  "pushConnection.executionError": "执行工作流时出现问题{error}",
  "pushConnection.executionError.openNode": "打开出错的节点",
  "pushConnection.executionError.details": "<br /><strong>{details}</strong>",
  "prompts.productTeamMessage": "我们的产品团队将亲自联系",
  "prompts.npsSurvey.recommendationQuestion": "您向朋友或同事推荐 n8n 的可能性有多大？",
  "prompts.npsSurvey.greatFeedbackTitle": "很高兴听到！我们可以联系您，看看如何让 n8n 对您更好吗？",
  "prompts.npsSurvey.defaultFeedbackTitle": "感谢您的反馈！我们很乐意了解我们如何改进。我们可以联系您吗？",
  "prompts.npsSurvey.notLikely": "不太可能",
  "prompts.npsSurvey.veryLikely": "很有可能",
  "prompts.npsSurvey.send": "发送",
  "prompts.npsSurvey.yourEmailAddress": "您的电子邮件地址",
  "prompts.npsSurvey.reviewUs": '如果您想提供更多帮助，请在 <a target="_blank" href="https://www.g2.com/products/n8n/reviews/start">G2</a> 上留下评论。',
  "prompts.npsSurvey.thanks": "感谢您的反馈",
  "renameAction.emptyName.message": "请输入名称，或按“esc”返回之前的名称",
  "renameAction.emptyName.title": "名称缺失",
  "renameAction.invalidName.title": "无效名称",
  "resourceLocator.id.placeholder": "输入 ID...",
  "resourceLocator.mode.id": "按 ID",
  "resourceLocator.mode.url": "按 URL",
  "resourceLocator.mode.list": "从列表中",
  "resourceLocator.mode.list.disabled.title": "更改为“已固定”模式以从列表中选择",
  "resourceLocator.mode.list.error.title": "无法加载列表",
  "resourceLocator.mode.list.error.description.checkCredentials": "请核对您的凭证",
  "resourceLocator.mode.list.error.description.noCredentials": "添加您的凭证",
  "resourceLocator.mode.list.noResults": "无结果",
  "resourceLocator.mode.list.openUrl": "打开 URL",
  "resourceLocator.mode.list.placeholder": "选择...",
  "resourceLocator.mode.list.searchRequired": "输入搜索词以显示结果",
  "resourceLocator.mode.list.addNewResource.vectorStoreInMemory": '创建密钥 "{resourceName}"',
  "resourceLocator.modeSelector.placeholder": "模式...",
  "resourceLocator.openSpecificResource": "在 {appName} 中打开 {entity}",
  "resourceLocator.openResource": "在 {appName} 中打开",
  "resourceLocator.placeholder.searchOrCreate": "搜索或创建...",
  "resourceLocator.placeholder.search": "搜索...",
  "resourceLocator.url.placeholder": "输入 URL...",
  "resourceMapper.autoMappingNotice": "在此模式下，确保传入的数据字段与 {serviceName} 中的 {fieldWord} 同名。（如果需要，请在此节点之前使用“编辑字段”节点来更改它们。）",
  "resourceMapper.mappingMode.label": "映射列模式",
  "resourceMapper.mappingMode.defineBelow.name": "手动映射每个列",
  "resourceMapper.mappingMode.defineBelow.description": "设置每个 {fieldWord} 的值",
  "resourceMapper.mappingMode.autoMapInputData.name": "自动映射",
  "resourceMapper.mappingMode.autoMapInputData.description": "查找与 {serviceName} 中的 {fieldWord} 匹配的传入数据",
  "resourceMapper.fetchingFields.message": "正在提取 {fieldWord}",
  "resourceMapper.fetchingFields.errorMessage": "无法获取 {fieldWord}。",
  "resourceMapper.fetchingFields.noFieldsFound": "在 {serviceName} 中未找到 {fieldWord}。",
  "resourceMapper.columnsToMatchOn.label": "要匹配的 {fieldWord}",
  "resourceMapper.columnsToMatchOn.multi.description": "在将 {nodeDisplayName} 中的行与此节点的输入项进行匹配时使用的 {fieldWord}。通常是一个 ID。",
  "resourceMapper.columnsToMatchOn.single.description": "在将 {nodeDisplayName} 中的行与此节点的输入项进行匹配时使用的 {fieldWord}。通常是一个 ID。",
  "resourceMapper.columnsToMatchOn.tooltip": "在查找要更新的行时要比较的 {fieldWord}",
  "resourceMapper.columnsToMatchOn.noFieldsFound": "在 {serviceName} 中未找到可用于匹配的 {fieldWord}。",
  "resourceMapper.valuesToSend.label": "要发送的值",
  "resourceMapper.valuesToUpdate.label": "要更新的值",
  "resourceMapper.usingToMatch": "（用于匹配）",
  "resourceMapper.usingToMatch.description": "此 {fieldWord} 不会被更新且无法删除，因为它用于匹配",
  "resourceMapper.removeField": "删除 {fieldWord}",
  "resourceMapper.mandatoryField.title": "此 {fieldWord} 是必填字段，无法删除",
  "resourceMapper.addFieldToSend": "添加要发送的 {fieldWord}",
  "resourceMapper.matching.title": "此 {fieldWord} 用于匹配，无法删除",
  "resourceMapper.addAllFields": "添加所有 {fieldWord}",
  "resourceMapper.removeAllFields": "删除所有 {fieldWord}",
  "resourceMapper.refreshFieldList": "刷新 {fieldWord} 列表",
  "resourceMapper.staleDataWarning.tooltip": "{fieldWord} 已过时。刷新以查看更改。",
  "resourceMapper.staleDataWarning.notice": "刷新以查看更新的字段",
  "resourceMapper.attemptToConvertTypes.displayName": "尝试转换类型",
  "resourceMapper.attemptToConvertTypes.description": "映射字段时尝试转换类型",
  "runData.openSubExecutionSingle": "查看子执行",
  "runData.openSubExecutionWithId": "查看子执行 {id}",
  "runData.openParentExecution": "检查父执行 {id}",
  "runData.emptyItemHint": "这是一个项目，但它为空。",
  "runData.emptyArray": "[空数组]",
  "runData.emptyString": "[空]",
  "runData.emptyObject": "[空对象]",
  "runData.unnamedField": "[未命名字段]",
  "runData.switchToBinary.info": "此项仅具有",
  "runData.switchToBinary.binary": "二进制数据",
  "runData.linking.hint": "链接显示的输入和输出运行",
  "runData.unlinking.hint": "取消链接显示的输入和输出运行",
  "runData.binary": "二进制",
  "runData.copyItemPath": "复制项目路径",
  "runData.copyItemPath.toast": "已复制项目路径",
  "runData.copyParameterPath": "复制参数路径",
  "runData.copyParameterPath.toast": "已复制参数路径",
  "runData.copyValue": "复制选择",
  "runData.copyValue.toast": "已复制输出数据",
  "runData.copyToClipboard": "复制到剪贴板",
  "runData.copyDisabled": "首先点击您要复制的输出数据，然后点击此按钮。",
  "runData.editOutput": "编辑输出",
  "runData.editOutputInvalid": "输出数据有问题",
  "runData.editOutputInvalid.singleQuote": '意外的单引号。请改用双引号 (")',
  "runData.editOutputInvalid.onLine": "在第 {line} 行：",
  "runData.editOutputInvalid.atPosition": "（在位置 {position}）",
  "runData.editValue": "编辑值",
  "runData.executionStatus.success": "执行成功",
  "runData.executionStatus.failed": "执行失败",
  "runData.downloadBinaryData": "下载",
  "runData.executeNode": "测试节点",
  "runData.executionTime": "执行时间",
  "runData.fileExtension": "文件扩展名",
  "runData.directory": "目录",
  "runData.fileName": "文件名",
  "runData.invalidPinnedData": "无效的固定数据",
  "runData.items": "项目",
  "runData.json": "JSON",
  "runData.rendered": "渲染",
  "runData.schema": "架构",
  "runData.mimeType": "MIME 类型",
  "runData.fileSize": "文件大小",
  "runData.ms": "毫秒",
  "runData.noBinaryDataFound": "未找到二进制数据",
  "runData.noData": "无数据",
  "runData.noTextDataFound": "未找到文本数据",
  "runData.nodeReturnedALargeAmountOfData": "节点返回了大量数据",
  "runData.output": "输出",
  "runData.showBinaryData": "查看",
  "runData.startTime": "开始时间",
  "runData.table": "表格",
  "runData.table.viewSubExecution": "查看子执行 {id}",
  "runData.pindata.learnMore": "了解更多",
  "runData.pindata.thisDataIsPinned": "此数据已固定。",
  "runData.pindata.unpin": "取消固定",
  "runData.editor.save": "保存",
  "runData.editor.cancel": "取消",
  "runData.editor.copyDataInfo": "您可以复制以前执行的数据并将其粘贴到上面。",
  "runData.aiContentBlock.startedAt": "开始于 {startTime}",
  "runData.aiContentBlock.tokens": "{count} 个令牌",
  "runData.aiContentBlock.tokens.prompt": "提示：",
  "runData.aiContentBlock.tokens.completion": "补全：",
  "runData.trimmedData.title": "数据太大，无法显示",
  "runData.trimmedData.message": "数据太大，无法在此处显示。请在“执行”选项卡中查看完整详细信息。",
  "runData.trimmedData.loading": "加载数据",
  "runData.panel.actions.collapse": "折叠面板",
  "runData.panel.actions.open": "打开面板",
  "runData.panel.actions.popOut": "弹出式面板",
  "runData.panel.actions.sync": "与画布同步选择",
  "saveButton.save": "@:_reusableBaseText.save",
  "saveButton.saved": "已保存",
  "saveWorkflowButton.hint": "保存工作流",
  "saveButton.saving": "正在保存",
  settings,
  "settings.communityNodes": "社区节点",
  "settings.communityNodes.empty.title": "使用社区节点增强您的工作流",
  "settings.communityNodes.empty.verified.only.title": "通过经过验证的社区节点，为你的工作流程注入强劲动力。",
  "settings.communityNodes.empty.description": "安装我们社区提供的 {count} 多个节点包。",
  "settings.communityNodes.empty.verified.only.description": "您可以直接从节点面板安装由n8n验证过的社区及合作伙伴构建的节点包。已安装的包将在此处显示。",
  "settings.communityNodes.empty.description.no-packages": "安装我们社区提供的节点包。",
  "settings.communityNodes.empty.installPackageLabel": "安装社区节点",
  "settings.communityNodes.npmUnavailable.warning": '要使用此功能，请<a href="{npmUrl}" target="_blank" title="如何安装 npm">安装 npm</a> 并重新启动 n8n。',
  "settings.communityNodes.packageNodes.label": "{count} 个节点 | {count} 个节点",
  "settings.communityNodes.updateAvailable.tooltip": "有更新的版本可用",
  "settings.communityNodes.viewDocsAction.label": "文档",
  "settings.communityNodes.uninstallAction.label": "卸载包",
  "settings.communityNodes.upToDate.tooltip": "您是最新版本",
  "settings.communityNodes.failedToLoad.tooltip": "此包有问题，请尝试卸载它，然后重新安装以解决此问题",
  "settings.communityNodes.fetchError.title": "获取已安装的包时出现问题",
  "settings.communityNodes.fetchError.message": "您的互联网连接或 n8n 实例可能有问题",
  "settings.communityNodes.installModal.title": "安装社区节点",
  "settings.communityNodes.installModal.description": "在 npm 公共注册表中查找要添加的社区节点。",
  "settings.communityNodes.browseButton.label": "浏览",
  "settings.communityNodes.installModal.packageName.label": "npm 包名称",
  "settings.communityNodes.installModal.packageName.tooltip": "<img src='/static/community_package_tooltip_img.png'/><p>这是 <a href='{npmURL}'>npmjs.com</a> 上的包的标题</p><p>通过在 {'@'} 之后添加特定版本来安装特定版本，例如，<code>package-name{'@'}0.15.0</code></p>",
  "settings.communityNodes.installModal.packageName.placeholder": "例如，n8n-nodes-chatwork",
  "settings.communityNodes.installModal.checkbox.label": "我理解从公共来源安装未经认证的代码的风险。",
  "settings.communityNodes.installModal.installButton.label": "安装",
  "settings.communityNodes.installModal.installButton.label.loading": "正在安装",
  "settings.communityNodes.installModal.error.packageNameNotValid": "包名称必须以 n8n-nodes- 开头",
  "settings.communityNodes.messages.install.success": "已安装包",
  "settings.communityNodes.messages.install.error": "安装新包时出错",
  "settings.communityNodes.messages.uninstall.error": "卸载包时出现问题",
  "settings.communityNodes.messages.uninstall.success.title": "已卸载包",
  "settings.communityNodes.messages.update.success.title": "已更新包",
  "settings.communityNodes.messages.update.success.message": "{packageName} 已更新到版本 {version}",
  "settings.communityNodes.messages.update.error.title": "更新包时出现问题",
  "settings.communityNodes.confirmModal.uninstall.title": "卸载包？",
  "settings.communityNodes.confirmModal.uninstall.message": "任何使用来自 {packageName} 包的节点的工作流都将无法运行。您确定吗？",
  "settings.communityNodes.confirmModal.uninstall.buttonLabel": "卸载包",
  "settings.communityNodes.confirmModal.uninstall.buttonLoadingLabel": "正在卸载",
  "settings.communityNodes.confirmModal.update.title": "更新社区节点包？",
  "settings.communityNodes.confirmModal.update.message": "您即将将 {packageName} 更新到版本 {version}",
  "settings.communityNodes.confirmModal.update.warning": "此版本尚未经过 n8n 验证，可能包含破坏性更改或错误。",
  "settings.communityNodes.confirmModal.update.description": "我们建议您停用使用该包的任何节点的工作流，并在更新完成后重新激活它们",
  "settings.communityNodes.confirmModal.update.buttonLabel": "更新包",
  "settings.communityNodes.confirmModal.update.buttonLoadingLabel": "正在更新...",
  "settings.goBack": "返回",
  "settings.personal": "个人",
  "settings.personal.basicInformation": "基本信息",
  "settings.personal.personalSettings": "个人设置",
  "settings.personal.personalSettingsUpdated": "个人详细信息已更新",
  "settings.personal.personalSettingsUpdatedError": "更新您的详细信息时出现问题",
  "settings.personal.role.tooltip.default": "新用户默认角色",
  "settings.personal.role.tooltip.member": "创建和管理您自己的工作流和凭证",
  "settings.personal.role.tooltip.admin": "完全访问权限，可管理工作流、标签、凭证、项目、用户等。",
  "settings.personal.role.tooltip.owner": "管理一切{cloudAccess}",
  "settings.personal.role.tooltip.cloud": "并访问云端仪表盘",
  "settings.personal.save": "保存",
  "settings.personal.security": "安全",
  "settings.signup.signUpInviterInfo": "{firstName} {lastName} 邀请您使用 n8n",
  "settings.users": "用户",
  "settings.users.count": "{count} 位用户 | {count} 位用户",
  "settings.users.search.placeholder": "按姓名或邮箱搜索",
  "settings.users.confirmDataHandlingAfterDeletion": "我们应该如何处理他们的数据？",
  "settings.users.confirmUserDeletion": "是否确定要删除此受邀请用户？",
  "settings.users.delete": "删除",
  "settings.users.deleteConfirmationMessage": "键入“删除所有数据”进行确认",
  "settings.users.deleteConfirmationText": "删除所有数据",
  "settings.users.deleteUser": "删除 {user}",
  "settings.users.actions.delete": "删除用户",
  "settings.users.actions.reinvite": "重新发送邀请",
  "settings.users.actions.copyInviteLink": "复制邀请链接",
  "settings.users.actions.copyPasswordResetLink": "复制密码重置链接",
  "settings.users.actions.allowSSOManualLogin": "允许手动登录",
  "settings.users.actions.disallowSSOManualLogin": "禁止手动登录",
  "settings.users.deleteWorkflowsAndCredentials": "删除他们的工作流和凭证",
  "settings.users.emailInvitesSent": "已向 {emails} 发送邀请电子邮件",
  "settings.users.emailInvitesSentError": "无法邀请 {emails}",
  "settings.users.emailSentTo": "已向 {email} 发送电子邮件",
  "settings.users.invalidEmailError": "{email} 不是有效的电子邮件",
  "settings.users.inviteLink.copy": "复制邀请链接",
  "settings.users.inviteLink.error": "无法检索邀请链接",
  "settings.users.invite": "邀请",
  "settings.users.invite.tooltip": "已激活 SAML 登录。用户应在 IdP 中创建，并在首次登录时在 n8n 中配置。",
  "settings.users.inviteNewUsers": "邀请新用户",
  "settings.users.copyInviteUrls": "现在，您可以将邀请链接直接发送给您的用户",
  "settings.users.inviteResent": "已重新发送邀请",
  "settings.users.inviteUser": "邀请用户",
  "settings.users.inviteUser.inviteUrl": "创建邀请链接",
  "settings.users.inviteXUser": "邀请 {count} 位用户",
  "settings.users.inviteXUser.inviteUrl": "创建 {count} 个邀请链接",
  "settings.users.inviteUrlCreated": "邀请链接已复制到剪贴板",
  "settings.users.inviteUrlCreated.message": "将邀请链接发送给您的受邀者以进行激活",
  "settings.users.passwordResetUrlCreated": "密码重置链接已复制到剪贴板",
  "settings.users.passwordResetUrlCreated.message": "将重置链接发送给您的用户以供他们重置密码",
  "settings.users.passwordResetLinkError": "无法获取密码重置链接",
  "settings.users.allowSSOManualLogin": "允许手动登录",
  "settings.users.allowSSOManualLogin.message": "用户现在可以手动登录和通过 SSO 登录",
  "settings.users.disallowSSOManualLogin": "禁止手动登录",
  "settings.users.disallowSSOManualLogin.message": "用户现在必须仅通过 SSO 登录",
  "settings.users.multipleInviteUrlsCreated": "已创建邀请链接",
  "settings.users.multipleInviteUrlsCreated.message": "将邀请链接发送给您的受邀者以进行激活",
  "settings.users.newEmailsToInvite": "要邀请的新用户电子邮件地址",
  "settings.users.noUsersToInvite": "没有要邀请的用户",
  "settings.users.setupMyAccount": "设置我的所有者帐户",
  "settings.users.setupToInviteUsers": "要邀请用户，请设置您自己的帐户",
  "settings.users.setupToInviteUsersInfo": '除非您升级，否则受邀用户将无法看到其他用户的工作流和凭证。<a href="https://docs.n8n.io/user-management/" target="_blank">更多信息</a><br /><br />',
  "settings.users.smtpToAddUsersWarning": '添加用户之前设置 SMTP（以便 n8n 可以向他们发送邀请电子邮件）。<a target="_blank" href="https://docs.n8n.io/hosting/authentication/user-management-self-hosted/">说明</a>',
  "settings.users.transferWorkflowsAndCredentials": "将他们的工作流和凭证转移给其他用户或项目",
  "settings.users.transferWorkflowsAndCredentials.user": "要转移到的用户或项目",
  "settings.users.transferWorkflowsAndCredentials.placeholder": "选择项目或用户",
  "settings.users.transferredToUser": "数据已转移到 {projectName}",
  "settings.users.userNotFound": "用户未找到",
  "settings.users.userDeleted": "已删除用户",
  "settings.users.userDeletedError": "删除用户时出现问题",
  "settings.users.userInvited": "已邀请用户",
  "settings.users.userInvitedError": "无法邀请用户",
  "settings.users.userReinviteError": "无法重新邀请用户",
  "settings.users.userToTransferTo": "要转移到的用户",
  "settings.users.usersEmailedError": "无法发送邀请电子邮件",
  "settings.users.usersInvited": "已邀请用户",
  "settings.users.usersInvitedError": "无法邀请用户",
  "settings.users.advancedPermissions.warning": "{link} 以解锁创建其他管理员用户的能力",
  "settings.users.userRoleUpdated": "已保存更改",
  "settings.users.userRoleUpdated.message": "{user} 已成功更新为 {role}",
  "settings.users.userRoleUpdatedError": "无法更新角色",
  "settings.users.table.update.error": "无法更新表格",
  "settings.users.table.header.user": "用户",
  "settings.users.table.header.accountType": "账户类型",
  "settings.users.table.header.2fa": "双因素身份验证",
  "settings.users.table.header.lastActive": "上次活动",
  "settings.users.table.row.allProjects": "所有项目",
  "settings.users.table.row.personalProject": "个人项目",
  "settings.users.table.row.deleteUser": "移除用户",
  "settings.users.table.row.role.description.admin": "完全访问所有工作流、凭证、项目、用户等",
  "settings.users.table.row.role.description.member": "管理和创建自己的工作流和凭据",
  "settings.users.table.row.2fa.enabled": "已启用",
  "settings.users.table.row.2fa.disabled": "已禁用",
  "settings.api": "API",
  "settings.api.scopes.upgrade": "{link} 以解锁修改API密钥权限范围的功能",
  "settings.n8napi": "n8n API",
  "settings.log-streaming": "日志流",
  "settings.log-streaming.heading": "日志流",
  "settings.log-streaming.add": "添加新目标",
  "settings.log-streaming.actionBox.title": "在企业版计划中可用",
  "settings.log-streaming.actionBox.description": "日志流作为一项付费功能提供。了解有关它的更多信息。",
  "settings.log-streaming.actionBox.button": "查看计划",
  "settings.log-streaming.infoText": '将日志发送到您选择的外部端点。您还可以使用环境变量将日志写入文件或控制台。 <a href="https://docs.n8n.io/log-streaming/" target="_blank">更多信息</a>',
  "settings.log-streaming.addFirstTitle": "设置目标以开始使用",
  "settings.log-streaming.addFirst": "通过点击按钮并选择目标类型来添加您的第一个目标。",
  "settings.log-streaming.saving": "正在保存",
  "settings.log-streaming.delete": "删除",
  "settings.log-streaming.continue": "继续",
  "settings.log-streaming.selecttype": "选择要创建的类型",
  "settings.log-streaming.selecttypehint": "选择新日志流目标的类型",
  "settings.log-streaming.tab.settings": "设置",
  "settings.log-streaming.tab.events": "事件",
  "settings.log-streaming.tab.events.title": "选择要订阅的组或单个事件：",
  "settings.log-streaming.tab.events.anonymize": "匿名化敏感数据",
  "settings.log-streaming.tab.events.anonymize.info": "包含姓名或电子邮件等个人信息的字段将被匿名化",
  "settings.log-streaming.eventGroup.n8n.ai": "AI节点日志",
  "settings.log-streaming.eventGroup.n8n.audit": "审计事件",
  "settings.log-streaming.eventGroup.n8n.audit.info": "当用户详细信息或其他审计数据更改时，将发送事件",
  "settings.log-streaming.eventGroup.n8n.workflow": "工作流事件",
  "settings.log-streaming.eventGroup.n8n.workflow.info": "将发送工作流执行事件",
  "settings.log-streaming.eventGroup.n8n.user": "用户",
  "settings.log-streaming.eventGroup.n8n.node": "节点执行",
  "settings.log-streaming.eventGroup.n8n.node.info": "每次执行节点时都会发送逐步执行事件。请注意，这可能导致高频率的日志事件，可能不适合一般使用。",
  "settings.log-streaming.eventGroup.n8n.runner": "运行器任务",
  "settings.log-streaming.eventGroup.n8n.runner.info": "当从任务运行器请求执行代码节点时，以及从运行器收到带有结果的响应时，将发送一个事件。",
  "settings.log-streaming.eventGroup.n8n.queue": "队列事件",
  "settings.log-streaming.eventGroup.n8n.queue.info": "当队列相关事件发生时发送事件，例如入队、出队、完成、失败或停滞。",
  "settings.log-streaming.eventGroup.n8n.worker": "工作进程",
  "settings.log-streaming.$$AbstractMessageEventBusDestination": "通用",
  "settings.log-streaming.$$MessageEventBusDestinationWebhook": "Webhook",
  "settings.log-streaming.$$MessageEventBusDestinationSentry": "Sentry",
  "settings.log-streaming.$$MessageEventBusDestinationRedis": "Redis",
  "settings.log-streaming.$$MessageEventBusDestinationSyslog": "Syslog",
  "settings.log-streaming.destinationDelete.cancelButtonText": "",
  "settings.log-streaming.destinationDelete.confirmButtonText": "是，删除",
  "settings.log-streaming.destinationDelete.headline": "删除目标？",
  "settings.log-streaming.destinationDelete.message": "您确定要删除“{destinationName}”吗？",
  "settings.log-streaming.addDestination": "添加新目标",
  "settings.log-streaming.destinations": "日志目标",
  "settings.api.trial.upgradePlan.title": "升级以使用 API",
  "settings.api.trial.upgradePlan.description": `为了防止滥用，我们在您的试用期间限制对您工作区的 API 访问。如果这妨碍了您对 n8n 的评估，请联系 <a href="mailto:support{'@'}n8n.io">support{'@'}n8n.io</a>`,
  "settings.api.trial.upgradePlan.cta": "升级计划",
  "settings.api.create.description": '使用 <a href="https://docs.n8n.io/api" target="_blank">n8n API</a> 以编程方式控制 n8n',
  "settings.api.create.button": "创建 API 密钥",
  "settings.api.create.button.loading": "正在创建 API 密钥...",
  "settings.api.create.error": "创建 API 密钥失败。",
  "settings.api.edit.error": "API密钥更新失败。",
  "settings.api.delete.title": "删除此 API 密钥？",
  "settings.api.delete.description": "任何使用此 API 密钥的应用程序将不再有权访问 n8n。此操作无法撤消。",
  "settings.api.delete.button": "永久删除",
  "settings.api.delete.error": "删除 API 密钥失败。",
  "settings.api.delete.toast": "API 密钥已删除",
  "settings.api.create.toast": "API密钥已创建",
  "settings.api.update.toast": "API密钥已更新",
  "settings.api.creationTime": "API密钥创建于{time}",
  "settings.api.expirationTime": "于 {time} 过期",
  "settings.api.expired": "该API密钥已过期",
  "settings.api.neverExpires": "永不过期",
  "settings.api.view.copy.toast": "API 密钥已复制到剪贴板",
  "settings.api.view.apiPlayground": "API 游乐场",
  "settings.api.view.info": "使用您的 API 密钥以编程方式使用{apiAction}控制 n8n。但如果您只想触发工作流，请考虑改用{webhookAction}。",
  "settings.api.view.copy": "请务必立即复制您的 API 密钥，因为您将无法再次看到它。",
  "settings.api.view.info.api": "n8n API",
  "settings.api.view.info.webhook": "webhook 节点",
  "settings.api.view.tryapi": "使用以下方式试用它",
  "settings.api.view.more-details": "您可以在以下位置找到更多详细信息",
  "settings.api.view.external-docs": "API 文档",
  "settings.api.view.error": "无法检查是否已存在 API 密钥。",
  "settings.api.view.modal.form.label": "标签",
  "settings.api.view.modal.form.expiration": "到期",
  "settings.api.view.modal.form.expirationText": "API密钥将于{expirationDate}过期",
  "settings.api.view.modal.form.label.placeholder": "例如 内部项目",
  "settings.api.view.modal.form.expiration.custom": "自定义",
  "settings.api.view.modal.form.expiration.days": "{numberOfDays} 天",
  "settings.api.view.modal.form.expiration.none": "永久有效",
  "settings.api.view.modal.title.created": "API密钥已创建",
  "settings.api.view.modal.title.create": "创建API密钥",
  "settings.api.view.modal.title.edit": "编辑API密钥",
  "settings.api.view.modal.done.button": "完成",
  "settings.api.view.modal.save.button": "保存",
  "settings.api.scopes.placeholder": "选择",
  "settings.api.scopes.selectAll": "全选",
  "settings.api.scopes.label": "范围",
  "settings.version": "版本",
  "settings.usageAndPlan.title": "使用情况和计划",
  "settings.usageAndPlan.description": "您正在使用{name} {type}",
  "settings.usageAndPlan.plan": "计划",
  "settings.usageAndPlan.callOut": "{link}免费（永久）解锁选定的付费功能",
  "settings.usageAndPlan.callOut.link": "解锁",
  "settings.usageAndPlan.edition": "版本",
  "settings.usageAndPlan.error": "@:_reusableBaseText.error",
  "settings.usageAndPlan.activeWorkflows": "活动工作流",
  "settings.usageAndPlan.activeWorkflows.unlimited": "@:_reusableBaseText.unlimited",
  "settings.usageAndPlan.activeWorkflows.count": "{count} / {limit}",
  "settings.usageAndPlan.activeWorkflows.hint": "具有多个触发器的活动工作流会计数多次",
  "settings.usageAndPlan.button.activation": "输入激活密钥",
  "settings.usageAndPlan.button.plans": "查看计划",
  "settings.usageAndPlan.button.manage": "管理计划",
  "settings.usageAndPlan.dialog.activation.title": "输入激活密钥",
  "settings.usageAndPlan.dialog.activation.label": "激活密钥",
  "settings.usageAndPlan.dialog.activation.activate": "@:_reusableBaseText.activate",
  "settings.usageAndPlan.dialog.activation.cancel": "@:_reusableBaseText.cancel",
  "settings.usageAndPlan.license.activation.error.title": "激活失败",
  "settings.usageAndPlan.license.activation.success.title": "许可证已激活",
  "settings.usageAndPlan.license.activation.success.message": "您的 {name} {type} 已成功激活。",
  "settings.usageAndPlan.license.communityRegistered.tooltip": "您已注册您的电子邮件，以解锁您的社区计划中的其他功能",
  "settings.externalSecrets.title": "外部密钥",
  "settings.externalSecrets.info": "连接外部密钥工具，用于跨环境集中凭据管理，并增强系统安全性。",
  "settings.externalSecrets.info.link": "更多信息",
  "settings.externalSecrets.actionBox.title": "在企业版计划中可用",
  "settings.externalSecrets.actionBox.description": "连接外部密钥工具，用于跨实例集中凭据管理。 {link}",
  "settings.externalSecrets.actionBox.description.link": "更多信息",
  "settings.externalSecrets.actionBox.buttonText": "查看计划",
  "settings.externalSecrets.card.setUp": "设置",
  "settings.externalSecrets.card.deprecated": "已弃用",
  "settings.externalSecrets.card.secretsCount": "{count} 个密钥",
  "settings.externalSecrets.card.connectedAt": "已连接 {date}",
  "settings.externalSecrets.card.connected": "已启用",
  "settings.externalSecrets.card.disconnected": "已禁用",
  "settings.externalSecrets.card.actionDropdown.setup": "编辑连接",
  "settings.externalSecrets.card.actionDropdown.reload": "重新加载密钥",
  "settings.externalSecrets.card.reload.success.title": "重新加载成功",
  "settings.externalSecrets.card.reload.success.description": "所有密钥已从 {provider} 重新加载。",
  "settings.externalSecrets.provider.title": "提交并推送更改",
  "settings.externalSecrets.provider.description": "选择要在提交中暂存的文件并添加提交消息。",
  "settings.externalSecrets.provider.buttons.cancel": "取消",
  "settings.externalSecrets.provider.buttons.save": "保存",
  "settings.externalSecrets.provider.buttons.saving": "正在保存",
  "settings.externalSecrets.card.connectedSwitch.title": "启用 {provider}",
  "settings.externalSecrets.provider.save.success.title": "提供商设置已成功保存",
  "settings.externalSecrets.provider.connected.success.title": "提供商已成功连接",
  "settings.externalSecrets.provider.disconnected.success.title": "提供商已成功断开连接",
  "settings.externalSecrets.provider.testConnection.success.connected": "服务已启用，{count} 个密钥可在 {provider} 上使用。",
  "settings.externalSecrets.provider.testConnection.success.connected.usage": "通过将参数设置为表达式并输入以下内容，在凭据中使用密钥：{code}。",
  "settings.externalSecrets.provider.testConnection.success.connected.docs": "更多信息",
  "settings.externalSecrets.provider.testConnection.success": "与 {provider} 的连接已成功执行。启用服务以在凭据中使用密钥。",
  "settings.externalSecrets.provider.testConnection.error.connected": "连接不成功，请检查您的 {provider} 设置",
  "settings.externalSecrets.provider.testConnection.error": "连接不成功，请检查您的 {provider} 设置",
  "settings.externalSecrets.provider.closeWithoutSaving.title": "关闭而不保存？",
  "settings.externalSecrets.provider.closeWithoutSaving.description": "您确定要放弃对 {provider} 设置所做的更改吗？",
  "settings.externalSecrets.provider.closeWithoutSaving.cancel": "关闭",
  "settings.externalSecrets.provider.closeWithoutSaving.confirm": "继续编辑",
  "settings.externalSecrets.docs": "https://docs.n8n.io/external-secrets/",
  "settings.externalSecrets.docs.use": "https://docs.n8n.io/external-secrets/#use-secrets-in-n8n-credentials",
  "settings.sourceControl.title": "环境",
  "settings.sourceControl.actionBox.title": "在企业版计划中可用",
  "settings.sourceControl.actionBox.description": "使用多个实例用于不同的环境（开发、生产等），通过 Git 存储库在它们之间进行部署。",
  "settings.sourceControl.actionBox.description.link": "更多信息",
  "settings.sourceControl.actionBox.buttonText": "查看计划",
  "settings.sourceControl.connection.error": "源代码控制连接失败",
  "settings.sourceControl.connection.error.message": "我们找不到连接到此实例的存储库。请仔细检查此实例上的{link}。",
  "settings.sourceControl.connection.error.link": "Git 配置",
  "settings.sourceControl.description": "使用多个实例用于不同的环境（开发、生产等），通过 Git 存储库在它们之间进行部署。{link}",
  "settings.sourceControl.description.link": "更多信息",
  "settings.sourceControl.gitConfig": "Git 配置",
  "settings.sourceControl.repoUrl": "Git 存储库 URL (SSH)",
  "settings.sourceControl.repoUrlPlaceholder": "例如：git{'@'}github.com:my-team/my-repository",
  "settings.sourceControl.repoUrlInvalid": "Git 存储库 URL 无效",
  "settings.sourceControl.authorName": "提交作者姓名",
  "settings.sourceControl.authorEmail": "提交作者电子邮件",
  "settings.sourceControl.authorEmailInvalid": "提供的电子邮件不正确",
  "settings.sourceControl.sshKey": "SSH 密钥",
  "settings.sourceControl.sshKeyDescription": "将 SSH 密钥粘贴到您的 git 存储库/帐户设置中。{link}",
  "settings.sourceControl.sshKeyDescriptionLink": "更多信息",
  "settings.sourceControl.refreshSshKey": "刷新密钥",
  "settings.sourceControl.refreshSshKey.successful.title": "SSH 密钥已成功刷新",
  "settings.sourceControl.refreshSshKey.error.title": "SSH 密钥刷新失败",
  "settings.sourceControl.button.continue": "继续",
  "settings.sourceControl.button.connect": "连接",
  "settings.sourceControl.button.disconnect": "断开 Git 连接",
  "settings.sourceControl.button.save": "保存设置",
  "settings.sourceControl.instanceSettings": "实例设置",
  "settings.sourceControl.branches": "连接到此 n8n 实例的分支",
  "settings.sourceControl.protected": "{bold}：防止编辑工作流（建议用于生产环境）。",
  "settings.sourceControl.protected.bold": "受保护的实例",
  "settings.sourceControl.color": "颜色",
  "settings.sourceControl.switchBranch.title": "切换到 {branch} 分支",
  "settings.sourceControl.switchBranch.description": "请确认您要将当前 n8n 实例切换到分支：{branch}",
  "settings.sourceControl.sync.prompt.title": "同步 {branch} 分支中的更改",
  "settings.sourceControl.sync.prompt.description": "您 n8n 实例上的所有更改都将与远程 Git 存储库上的 {branch} 分支同步。将执行以下 Git 序列：pull > commit > push。",
  "settings.sourceControl.sync.prompt.placeholder": "提交消息",
  "settings.sourceControl.sync.prompt.error": "请输入提交消息",
  "settings.sourceControl.button.push": "推送",
  "settings.sourceControl.button.pull": "拉取",
  "settings.sourceControl.button.pull.forbidden": "只有实例所有者或实例管理员才能拉取更改。",
  "settings.sourceControl.button.push.forbidden": "你无法从受保护的实例推送更改。",
  "settings.sourceControl.modals.push.title": "提交并推送更改",
  "settings.sourceControl.modals.push.description": "将提交以下内容：",
  "settings.sourceControl.modals.push.description.learnMore": "更多信息",
  "settings.sourceControl.modals.push.filesToCommit": "要提交的文件",
  "settings.sourceControl.modals.push.filter": "已应用筛选器。显示 {count} 个 {entity}。",
  "settings.sourceControl.modals.push.workflowsToCommit": "选择工作流",
  "settings.sourceControl.modals.push.everythingIsUpToDate": "一切都是最新的",
  "settings.sourceControl.modals.push.noWorkflowChanges": "没有工作流更改，但将提交以下内容：{link}",
  "settings.sourceControl.modals.push.noWorkflowChanges.moreInfo": "更多信息",
  "settings.sourceControl.modals.push.commitMessage": "提交消息",
  "settings.sourceControl.modals.push.commitMessage.placeholder": "例如：我的提交",
  "settings.sourceControl.modals.push.buttons.cancel": "取消",
  "settings.sourceControl.modals.push.buttons.save": "提交并推送",
  "settings.sourceControl.modals.push.success.title": "推送成功",
  "settings.sourceControl.modals.push.success.description": "已提交并推送到您的远程存储库",
  "settings.sourceControl.modals.push.projectAdmin.callout": "如果您想将工作流从您的个人空间推送出去，请先将它们移动到项目里。",
  "settings.sourceControl.status.modified": "已修改",
  "settings.sourceControl.status.deleted": "已删除",
  "settings.sourceControl.status.created": "新建",
  "settings.sourceControl.status.renamed": "已重命名",
  "settings.sourceControl.pull.oneLastStep.title": "最后一步",
  "settings.sourceControl.pull.oneLastStep.description": "您有新的凭据/变量。填写它们以确保您的工作流正常运行",
  "settings.sourceControl.pull.success.title": "拉取成功",
  "settings.sourceControl.pull.upToDate.title": "最新",
  "settings.sourceControl.pull.upToDate.description": "没有要从 Git 拉取的工作流更改",
  "settings.sourceControl.pull.upToDate.variables.title": "完成新变量的设置，以便在工作流中使用",
  "settings.sourceControl.pull.upToDate.variables.description": "审查变量",
  "settings.sourceControl.pull.upToDate.credentials.title": "完成新凭证的设置，以便在流程中使用",
  "settings.sourceControl.pull.upToDate.credentials.description": "审核凭证",
  "settings.sourceControl.modals.pull.title": "拉取更改",
  "settings.sourceControl.modals.pull.description": "这些工作流将被更新，并且对它们的任何本地更改都将被覆盖。要保留本地版本，请在拉取之前推送它。",
  "settings.sourceControl.modals.pull.description.learnMore": "更多信息",
  "settings.sourceControl.modals.pull.buttons.cancel": "@:_reusableBaseText.cancel",
  "settings.sourceControl.modals.pull.buttons.save": "拉取并覆盖",
  "settings.sourceControl.modals.disconnect.title": "断开 Git 存储库",
  "settings.sourceControl.modals.disconnect.message": "请确认您要断开此 n8n 实例与 Git 存储库的连接",
  "settings.sourceControl.modals.disconnect.confirm": "断开 Git 连接",
  "settings.sourceControl.modals.disconnect.cancel": "@:_reusableBaseText.cancel",
  "settings.sourceControl.modals.refreshSshKey.title": "刷新 SSH 密钥",
  "settings.sourceControl.modals.refreshSshKey.message": "这将删除当前的 SSH 密钥并创建一个新的密钥。您将无法再使用当前密钥进行身份验证。",
  "settings.sourceControl.modals.refreshSshKey.cancel": "取消",
  "settings.sourceControl.modals.refreshSshKey.confirm": "刷新密钥",
  "settings.sourceControl.loading.connecting": "正在连接",
  "settings.sourceControl.toast.connected.title": "Git 存储库已连接",
  "settings.sourceControl.toast.connected.message": "选择分支以完成配置",
  "settings.sourceControl.toast.connected.error": "连接到 Git 时出错",
  "settings.sourceControl.toast.disconnected.title": "Git 存储库已断开连接",
  "settings.sourceControl.toast.disconnected.message": "您无法再将您的实例与远程存储库同步",
  "settings.sourceControl.toast.disconnected.error": "断开与 Git 连接时出错",
  "settings.sourceControl.loading.pull": "正在从远程拉取",
  "settings.sourceControl.loading.checkingForChanges": "正在检查更改",
  "settings.sourceControl.loading.push": "正在推送到远程",
  "settings.sourceControl.lastUpdated": "上次更新时间：{date}，{time}",
  "settings.sourceControl.saved.title": "设置已成功保存",
  "settings.sourceControl.refreshBranches.tooltip": "重新加载分支列表",
  "settings.sourceControl.refreshBranches.success": "分支已成功刷新",
  "settings.sourceControl.refreshBranches.error": "刷新分支时出错",
  "settings.sourceControl.docs.url": "https://docs.n8n.io/source-control-environments/",
  "settings.sourceControl.docs.setup.url": "https://docs.n8n.io/source-control-environments/setup/",
  "settings.sourceControl.docs.setup.ssh.url": "https://docs.n8n.io/source-control-environments/setup/#step-3-set-up-a-deploy-key",
  "settings.sourceControl.docs.using.url": "https://docs.n8n.io/source-control-environments/using/",
  "settings.sourceControl.docs.using.pushPull.url": "https://docs.n8n.io/source-control-environments/using/push-pull",
  "settings.sourceControl.error.not.connected.title": "尚未启用环境",
  "settings.sourceControl.error.not.connected.message": "请转到 <a href='/settings/environments'>环境设置</a> 以首先连接 Git 存储库以激活此功能。",
  "showMessage.cancel": "@:_reusableBaseText.cancel",
  "showMessage.ok": "确定",
  "showMessage.showDetails": "显示详细信息",
  startupError,
  "startupError.message": "无法连接到服务器。 <a data-action='reload'>刷新</a> 以重试",
  "tagsDropdown.createTag": '创建标签 "{filter}"',
  "tagsDropdown.manageTags": "管理标签",
  "tagsDropdown.noMatchingTagsExist": "不存在匹配的标签",
  "tagsDropdown.noTagsExist": "不存在标签",
  "tagsDropdown.showError.message": "尝试创建“{name}”标签时出现问题",
  "tagsDropdown.showError.title": "无法创建标签",
  "tagsDropdown.typeToCreateATag": "键入以创建标签",
  "tagsManager.couldNotDeleteTag": "无法删除标签",
  "tagsManager.done": "完成",
  "tagsManager.manageTags": "管理标签",
  "tagsManager.showError.onFetch.title": "无法获取标签",
  "tagsManager.showError.onFetch.message": "尝试获取标签时出现问题",
  "tagsManager.showError.onCreate.message": "尝试创建标签“{escapedName}”时出现问题",
  "tagsManager.showError.onCreate.title": "无法创建标签",
  "tagsManager.showError.onDelete.message": "尝试删除标签“{escapedName}”时出现问题",
  "tagsManager.showError.onDelete.title": "无法删除标签",
  "tagsManager.showError.onUpdate.message": "尝试更新标签“{escapedName}”时出现问题",
  "tagsManager.showError.onUpdate.title": "无法更新标签",
  "tagsManager.showMessage.onDelete.title": "标签已删除",
  "tagsManager.showMessage.onUpdate.title": "标签已更新",
  "tagsManager.tagNameCannotBeEmpty": "标签名称不能为空",
  "tagsTable.areYouSureYouWantToDeleteThisTag": "您确定要删除此标签吗？",
  "tagsTable.cancel": "@:_reusableBaseText.cancel",
  "tagsTable.createTag": "创建标签",
  "tagsTable.deleteTag": "删除标签",
  "tagsTable.editTag": "编辑标签",
  "tagsTable.name": "@:_reusableBaseText.name",
  "tagsTable.noMatchingTagsExist": "不存在匹配的标签",
  "tagsTable.saveChanges": "保存更改？",
  "tagsTable.usage": "使用情况",
  "tagsTableHeader.addNew": "添加新标签",
  "tagsTableHeader.searchTags": "搜索标签",
  "tagsView.inUse": "{count} 个工作流 | {count} 个工作流",
  "tagsView.notBeingUsed": "未使用",
  "onboarding.title": "演示：{name}",
  "template.buttons.goBackButton": "返回",
  "template.buttons.useThisWorkflowButton": "使用此工作流",
  "template.details.appsInTheCollection": "此集合包含",
  "template.details.appsInTheWorkflow": "此工作流中的应用程序",
  "template.details.by": "作者：",
  "template.details.categories": "类别",
  "template.details.created": "创建于",
  "template.details.details": "详细信息",
  "template.details.times": "次",
  "template.details.viewed": "已查看",
  "template.byAuthor": "作者：{name}",
  "templates.allCategories": "所有类别",
  "templates.categoriesHeading": "类别",
  "templates.collection": "集合",
  "templates.collections": "集合",
  "templates.collectionsNotFound": "找不到集合",
  "templates.connectionWarning": "⚠️ 获取工作流模板时出现问题。请检查您的互联网连接。",
  "templates.heading": "工作流模板",
  "templates.shareWorkflow": "共享模板",
  "templates.noSearchResults": "未找到任何内容。请尝试调整搜索以查看更多内容。",
  "templates.searchPlaceholder": "搜索工作流",
  "templates.workflows": "工作流",
  "templates.workflowsNotFound": "找不到工作流",
  "textEdit.edit": "编辑",
  "userActivity.daysAgo": "{count} 天前",
  "userActivity.lastTime": "上次 {time}",
  "userActivity.today": "今天",
  "userActivity.yesterday": "昨天",
  "timeAgo.daysAgo": "%s 天前",
  "timeAgo.hoursAgo": "%s 小时前",
  "timeAgo.inDays": "%s 天后",
  "timeAgo.inHours": "%s 小时后",
  "timeAgo.inMinutes": "%s 分钟后",
  "timeAgo.inMonths": "%s 个月后",
  "timeAgo.inOneDay": "1 天后",
  "timeAgo.inOneHour": "1 小时后",
  "timeAgo.inOneMinute": "1 分钟后",
  "timeAgo.inOneMonth": "1 个月后",
  "timeAgo.inOneWeek": "1 周后",
  "timeAgo.inOneYear": "1 年后",
  "timeAgo.inWeeks": "%s 周后",
  "timeAgo.inYears": "%s 年后",
  "timeAgo.justNow": "刚刚",
  "timeAgo.minutesAgo": "%s 分钟前",
  "timeAgo.monthsAgo": "%s 个月前",
  "timeAgo.oneDayAgo": "1 天前",
  "timeAgo.oneHourAgo": "1 小时前",
  "timeAgo.oneMinuteAgo": "1 分钟前",
  "timeAgo.oneMonthAgo": "1 个月前",
  "timeAgo.oneWeekAgo": "1 周前",
  "timeAgo.oneYearAgo": "1 年前",
  "timeAgo.rightNow": "现在",
  "timeAgo.weeksAgo": "%s 周前",
  "timeAgo.yearsAgo": "%s 年前",
  "nodeIssues.credentials.notSet": "未设置 {type} 的凭据。",
  "nodeIssues.credentials.notAvailable": "凭据不可用",
  "nodeIssues.credentials.doNotExist": "名称为 {name} 的凭据不存在于 {type} 中。",
  "nodeIssues.credentials.doNotExist.hint": "您可以创建具有完全相同名称的凭据，然后在刷新时它们会被自动选中。",
  "nodeIssues.credentials.notIdentified": "名称为 {name} 的凭据存在于 {type} 中。",
  "nodeIssues.credentials.notIdentified.hint": "凭据未被明确标识。请选择正确的凭据。",
  "nodeIssues.input.missing": '没有节点连接到必需的输入 "{inputName}"',
  "ndv.trigger.moreInfo": "更多信息",
  "ndv.trigger.copiedTestUrl": "测试 URL 已复制到剪贴板",
  "ndv.trigger.webhookBasedNode.executionsHelp.inactive": '<b>创建工作流时</b>，点击"执行步骤"按钮，然后前往{service}并触发事件。这将启动一次执行记录，并显示在当前编辑器中。<br /><br /><b>完成工作流设计后</b>，请<a data-key="activate">激活</a>该流程。此后每当{service}中发生匹配事件时，工作流将自动执行。这些执行记录会显示在<a data-key="executions">执行列表</a>中，但不会出现在编辑器界面。',
  "ndv.trigger.webhookBasedNode.executionsHelp.active": '<b>构建工作流时</b>，点击"执行步骤"按钮，然后前往{service}触发一个事件。这将启动一次执行过程，执行结果会显示在当前编辑器中。<br /><br /><b>您的工作流也会自动执行</b>（因其已激活）。每当{service}中出现匹配事件时，该节点都会触发一次执行。这些执行会显示在<a data-key="executions">执行列表</a>中，但不会出现在编辑器里。',
  "ndv.trigger.webhookNode.listening": "正在侦听测试事件",
  "ndv.trigger.chatTrigger.openChat": "打开聊天窗口",
  "ndv.trigger.webhookNode.formTrigger.listening": "正在侦听测试表单提交",
  "ndv.trigger.webhookBasedNode.listening": "正在侦听您的触发事件",
  "ndv.trigger.webhookNode.requestHint": "向以下地址发送 {type} 请求：",
  "ndv.trigger.webhookBasedNode.serviceHint": "转到 {service} 并创建事件",
  "ndv.trigger.webhookBasedNode.chatTrigger.serviceHint": "在聊天中发送消息",
  "ndv.trigger.webhookBasedNode.formTrigger.serviceHint": "提交刚刚在新标签页中打开的测试表单",
  "ndv.trigger.webhookBasedNode.activationHint.inactive": '构建完工作流后，<a data-key="activate">激活它</a>以使其也持续侦听（您只是不会在这里看到那些执行）。',
  "ndv.trigger.webhookBasedNode.activationHint.active": "此节点也会在新的 {service} 事件发生时自动触发（但这些执行不会显示在这里）。",
  "ndv.trigger.pollingNode.activationHint.inactive": '构建完工作流后，<a data-key="activate">激活它</a>以使其也定期检查事件（您只是不会在这里看到那些执行）。',
  "ndv.trigger.pollingNode.activationHint.active": "此节点也会在新的 {service} 事件发生时自动触发（但这些执行不会显示在这里）。",
  "ndv.trigger.executionsHint.question": "此节点何时会触发我的流程？",
  "ndv.trigger.pollingNode.fetchingEvent": "正在获取事件",
  "ndv.trigger.pollingNode.fetchingHint": "此节点正在 {name} 中查找与您定义的事件类似的事件",
  "ndv.trigger.pollingNode.executionsHelp.inactive": '<b>在构建您的工作流时</b>，单击“获取”按钮以获取单个模拟事件。它将显示在此编辑器中。<br /><br /><b>当您对您的工作流感到满意时</b>，<a data-key="activate">激活</a> 它。然后，n8n 将定期检查 {service} 是否有新事件，如果找到任何事件，则执行此工作流。这些执行将显示在 <a data-key="executions">执行列表</a>中，而不是编辑器中。',
  "ndv.trigger.pollingNode.executionsHelp.active": '<b>在构建您的工作流时</b>，单击“获取”按钮以获取单个模拟事件。它将显示在此编辑器中。<br /><br /><b>您的工作流也会自动执行</b>，因为它已激活。n8n 将定期检查 {app_name} 是否有新事件，如果找到任何事件，则执行此工作流。这些执行将显示在 <a data-key="executions">执行列表</a>中，而不是编辑器中。',
  "ndv.trigger.webhookBasedNode.action": "从 {name} 中拉取事件",
  "ndv.search.placeholder.output": "搜索输出",
  "ndv.search.placeholder.input": "搜索所选节点",
  "ndv.search.placeholder.input.schema": "搜索先前节点的字段",
  "ndv.search.noMatch.title": "没有匹配项",
  "ndv.search.noNodeMatch.title": "没有匹配的节点",
  "ndv.search.noMatch.description": "请尝试更改或{link}筛选器以查看更多内容",
  "ndv.search.noMatch.description.link": "清除",
  "ndv.search.noMatchSchema.description": "要搜索字段内容而不仅仅是名称，请使用表格或 JSON 视图",
  "ndv.search.noMatchSchema.description.link": "清除筛选器",
  "ndv.search.items": "{matched} / {total} 项 | {matched} / {total} 项",
  "updatesPanel.andIs": "并且是",
  "updatesPanel.behindTheLatest": "落后于最新最好的 n8n",
  "updatesPanel.howToUpdateYourN8nVersion": "如何更新您的 n8n 版本",
  "updatesPanel.version": "{numberOfVersions} 个版本{howManySuffix}",
  "updatesPanel.weVeBeenBusy": "我们一直在忙着 ✨",
  "updatesPanel.youReOnVersion": "您正在使用 {currentVersionName} 版本，该版本已发布",
  "versionCard.breakingChanges": "重大更改",
  "versionCard.released": "已发布",
  "versionCard.securityUpdate": "安全更新",
  "versionCard.thisVersionHasASecurityIssue": "此版本存在安全问题。<br />此处列出此版本是为了完整性。",
  "versionCard.unknown": "未知",
  "versionCard.version": "版本",
  "workflowActivator.workflowIsActive": "工作流已激活",
  "workflowActivator.activateWorkflow": "激活工作流",
  "workflowActivator.deactivateWorkflow": "停用工作流",
  "workflowActivator.active": "活动",
  "workflowActivator.inactive": "非活动",
  "workflowActivator.showError.title": "工作流无法 {newStateName}",
  "workflowActivator.showMessage.activeChangedNodesIssuesExistTrue.message": "请在激活之前解决未决问题",
  "workflowActivator.showMessage.activeChangedNodesIssuesExistTrue.title": "激活工作流时出现问题",
  "workflowActivator.showMessage.activeChangedWorkflowIdUndefined.message": "请先保存，然后再激活",
  "workflowActivator.showMessage.activeChangedWorkflowIdUndefined.title": "激活工作流时出现问题",
  "workflowActivator.showMessage.displayActivationError.message.catchBlock": "抱歉，请求错误时出现问题",
  "workflowActivator.showMessage.displayActivationError.message.errorDataNotUndefined": "工作流激活时发生以下错误：",
  "workflowActivator.showMessage.displayActivationError.message.errorDataUndefined": "未知错误",
  "workflowActivator.showMessage.displayActivationError.title": "激活工作流时出现问题",
  "workflowActivator.theWorkflowIsSetToBeActiveBut": "工作流已激活，但无法启动。<br />单击以显示错误消息。",
  "workflowActivator.thisWorkflowHasNoTriggerNodes": "此工作流没有需要激活的触发节点",
  "workflowActivator.thisWorkflowIsArchived": "此工作流已存档，因此无法激活。",
  "workflowActivator.thisWorkflowHasOnlyOneExecuteWorkflowTriggerNode": "“执行工作流触发器”不需要激活，因为它由另一个工作流触发",
  "workflowDetails.share": "共享",
  "workflowDetails.active": "活动",
  "workflowDetails.addTag": "添加标签",
  "workflowDetails.chooseOrCreateATag": "选择或创建标签",
  "workflowExtraction.error.failure": "子工作流转换失败",
  "workflowExtraction.error.selectionGraph.inputEdgeToNonRoot": "非输入节点 '{node}' 接收来自当前选择之外节点的连接。",
  "workflowExtraction.error.selectionGraph.outputEdgeFromNonLeaf": "非输出节点 '{node}' 连接到当前选择范围之外的节点。",
  "workflowExtraction.error.selectionGraph.multipleInputNodes": "多个节点[{nodes}] 具有来自选择之外的输入。",
  "workflowExtraction.error.selectionGraph.multipleOutputNodes": "多个节点[{nodes}]的输出不在选择范围之内。",
  "workflowExtraction.error.selectionGraph.noContinuousPathFromRootToLeaf": "起始节点 '{start}' 没有选择到结束节点 '{end}' 的路径。",
  "workflowExtraction.error.selectionGraph.listHeader": '选择无效，因为存在以下错误：<br><br>{body}<br><br><a href="https://docs.n8n.io/workflows/subworkflow-conversion/" target="_blank">查看文档</a>了解更多信息。',
  "workflowExtraction.error.inputNodeHasMultipleInputBranches": "第一个节点 '{node}' 具有多个输入分支，而子工作流不支持这种情况。",
  "workflowExtraction.error.outputNodeHasMultipleOutputBranches": "最后一个节点 '{node}' 有多个输出分支，子工作流不支持。",
  "workflowExtraction.error.triggerSelected": "触发器无法转换为子工作流。请取消选择 {nodes}。",
  "workflowExtraction.error.subworkflowCreationFailed": "子工作流创建失败，已中止转换。",
  "workflowExtraction.success.title": "创建子工作流",
  "workflowExtraction.success.message": "在新的标签页中打开",
  "workflowExtraction.modal.title": "转换为子工作流",
  "workflowExtraction.modal.description": "将 {nodeCount} 个节点转换为新的子工作流 | 将 {nodeCount} 个节点转换为新的子工作流",
  "workflowHelpers.showMessage.title": "保存工作流时出现问题",
  "workflowOpen.active": "活动",
  "workflowOpen.couldNotLoadActiveWorkflows": "无法加载活动工作流",
  "workflowOpen.created": "已创建",
  "workflowOpen.filterWorkflows": "按标签筛选",
  "workflowOpen.name": "@:_reusableBaseText.name",
  "workflowOpen.openWorkflow": "打开工作流",
  "workflowOpen.searchWorkflows": "搜索工作流...",
  "workflowOpen.showError.title": "加载工作流时出现问题",
  "workflowOpen.showMessage.message": "这是当前工作流",
  "workflowOpen.showMessage.title": "工作流已打开",
  "workflowOpen.updated": "已更新",
  "workflowOpen.newWFButton.label": "添加工作流",
  "workflowOpen.newWFButton.title": "创建新工作流",
  "workflowPreview.showError.arrayEmpty": "必须具有节点数组",
  "workflowPreview.showError.missingWorkflow": "缺少工作流",
  "workflowPreview.showError.previewError.message": "无法预览工作流",
  "workflowPreview.showError.missingExecution": "缺少工作流执行",
  "workflowPreview.executionMode.showError.previewError.message": "无法预览工作流执行",
  "workflowPreview.showError.previewError.title": "预览错误",
  "workflowRun.noActiveConnectionToTheServer": "与服务器的连接丢失",
  "workflowRun.showError.deactivate": "停用工作流以执行",
  "workflowRun.showError.productionActive": "由于 {nodeName} 中的限制，n8n 无法同时侦听测试执行和生产执行",
  "workflowRun.showError.title": "运行工作流时出现问题",
  "workflowRun.showError.payloadTooLarge": "请执行整个工作流，而不是仅执行节点。（现有执行数据太大。）",
  "workflowRun.showError.resolveOutstandingIssues": "请在激活之前解决未决问题",
  "workflowRun.showMessage.message": "请在执行前修复它们",
  "workflowRun.showMessage.title": "工作流存在问题",
  "workflowSettings.callerIds": "可以调用此工作流的工作流 ID",
  "workflowSettings.callerIds.placeholder": "例如：14、18",
  "workflowSettings.callerPolicy": "此工作流可以由以下工作流调用",
  "workflowSettings.callerPolicy.options.any": "任何工作流",
  "workflowSettings.callerPolicy.options.workflowsFromPersonalProject": "由 {projectName} 创建的工作流",
  "workflowSettings.callerPolicy.options.workflowsFromTeamProject": "仅 {projectName} 中的工作流",
  "workflowSettings.callerPolicy.options.workflowsFromSameProject": "仅同一项目中的工作流",
  "workflowSettings.callerPolicy.options.workflowsFromAList": "选定的工作流",
  "workflowSettings.callerPolicy.options.none": "无其他工作流",
  "workflowSettings.defaultTimezone": "默认 - {defaultTimezoneValue}",
  "workflowSettings.defaultTimezoneNotValid": "默认时区无效",
  "workflowSettings.errorWorkflow": "错误工作流",
  "workflowSettings.executionOrder": "执行顺序",
  "workflowSettings.helpTexts.errorWorkflow": "如果当前工作流失败，则运行的第二个工作流。<br />第二个工作流应是一个“错误触发器”节点。",
  "workflowSettings.helpTexts.executionTimeout": "工作流在超时之前应等待的时间",
  "workflowSettings.helpTexts.executionTimeoutToggle": "是否在定义的时间后取消工作流执行",
  "workflowSettings.helpTexts.saveDataErrorExecution": "是否保存失败执行的数据",
  "workflowSettings.helpTexts.saveDataSuccessExecution": "是否保存成功完成执行的数据",
  "workflowSettings.helpTexts.saveExecutionProgress": "是否在每次节点执行后保存数据。这允许您在发生错误时从停止执行的位置恢复，但可能会增加延迟。",
  "workflowSettings.helpTexts.saveManualExecutions": "是否保存从编辑器手动启动的执行数据",
  "workflowSettings.helpTexts.timezone": "工作流应运行的时区。例如，由“cron”节点使用。",
  "workflowSettings.helpTexts.workflowCallerIds": "允许执行此工作流的工作流 ID（使用“执行工作流”节点）。ID 可以在工作流 URL 的末尾找到。多个 ID 用逗号分隔。",
  "workflowSettings.helpTexts.workflowCallerPolicy": "允许使用“执行工作流”节点调用此工作流的工作流",
  "workflowSettings.hours": "小时",
  "workflowSettings.minutes": "分钟",
  "workflowSettings.noWorkflow": "- 无工作流 -",
  "workflowSettings.save": "@:_reusableBaseText.save",
  "workflowSettings.saveDataErrorExecution": "保存失败的生产执行",
  "workflowSettings.saveDataErrorExecutionOptions.defaultSave": "默认 - {defaultValue}",
  "workflowSettings.saveDataErrorExecutionOptions.doNotSave": "不保存",
  "workflowSettings.saveDataErrorExecutionOptions.save": "@:_reusableBaseText.save",
  "workflowSettings.saveDataSuccessExecution": "保存成功的生产执行",
  "workflowSettings.saveDataSuccessExecutionOptions.defaultSave": "默认 - {defaultValue}",
  "workflowSettings.saveDataSuccessExecutionOptions.doNotSave": "不保存",
  "workflowSettings.saveDataSuccessExecutionOptions.save": "@:_reusableBaseText.save",
  "workflowSettings.saveExecutionProgress": "保存执行进度",
  "workflowSettings.saveExecutionProgressOptions.defaultSave": "默认 - {defaultValue}",
  "workflowSettings.saveExecutionProgressOptions.doNotSave": "不保存",
  "workflowSettings.saveExecutionProgressOptions.save": "@:_reusableBaseText.save",
  "workflowSettings.saveManualExecutions": "保存手动执行",
  "workflowSettings.saveManualOptions.defaultSave": "默认 - {defaultValue}",
  "workflowSettings.saveManualOptions.doNotSave": "不保存",
  "workflowSettings.saveManualOptions.save": "@:_reusableBaseText.save",
  "workflowSettings.seconds": "秒",
  "workflowSettings.selectOption": "选择选项",
  "workflowSettings.settingsFor": "{workflowName}（#{workflowId}）的工作流设置",
  "workflowSettings.showError.saveSettings1.errorMessage": "超时已激活，但设置为 0",
  "workflowSettings.showError.saveSettings1.message": "保存设置时出现问题",
  "workflowSettings.showError.saveSettings1.title": "保存设置时出现问题",
  "workflowSettings.showError.saveSettings2.errorMessage": "最大超时时间为：{hours} 小时、{minutes} 分钟、{seconds} 秒",
  "workflowSettings.showError.saveSettings2.message": "超时时间比允许的时间长",
  "workflowSettings.showError.saveSettings2.title": "保存设置时出现问题",
  "workflowSettings.showError.saveSettings3.title": "保存设置时出现问题",
  "workflowSettings.showMessage.saveSettings.title": "工作流设置已保存",
  "workflowSettings.timeoutAfter": "超时时间",
  "workflowSettings.timeoutWorkflow": "超时工作流",
  "workflowSettings.timezone": "时区",
  "workflowSettings.timeSavedPerExecution": "预计节省时间",
  "workflowSettings.timeSavedPerExecution.hint": "每次生产执行所需分钟数",
  "workflowSettings.timeSavedPerExecution.tooltip": "总节省时间已在概览页面中汇总。",
  "workflowHistory.title": "版本历史记录",
  "workflowHistory.content.title": "版本",
  "workflowHistory.content.editedBy": "编辑者",
  "workflowHistory.content.versionId": "版本 ID",
  "workflowHistory.content.actions": "操作",
  "workflowHistory.item.id": "ID：{id}",
  "workflowHistory.item.createdAt": "{date}，{time}",
  "workflowHistory.item.actions.restore": "恢复此版本",
  "workflowHistory.item.actions.clone": "克隆到新工作流",
  "workflowHistory.item.actions.open": "在新标签页中打开版本",
  "workflowHistory.item.actions.download": "下载",
  "workflowHistory.item.unsaved.title": "未保存的版本",
  "workflowHistory.item.latest": "最新保存的版本",
  "workflowHistory.empty": "尚无版本。",
  "workflowHistory.hint": "保存工作流以创建第一个版本！",
  "workflowHistory.limit": "版本历史记录限制为 {evaluatedPruneTime} 天",
  "workflowHistory.upgrade": "{link} 以激活完整历史记录",
  "workflowHistory.upgrade.link": "升级计划",
  "workflowHistory.action.error.title": "未能{action}",
  "workflowHistory.action.restore.modal.title": "恢复以前的工作流版本？",
  "workflowHistory.action.restore.modal.subtitle": "您的工作流将恢复到 {date} 的版本",
  "workflowHistory.action.restore.modal.text": "您的工作流当前处于活动状态，因此生产执行将立即开始使用恢复的版本。如果您想在恢复之前停用它，请单击 {buttonText}。",
  "workflowHistory.action.restore.modal.button.deactivateAndRestore": "停用并恢复",
  "workflowHistory.action.restore.modal.button.restore": "恢复",
  "workflowHistory.action.restore.modal.button.cancel": "取消",
  "workflowHistory.action.restore.success.title": "已成功恢复工作流版本",
  "workflowHistory.action.clone.success.title": "已成功克隆工作流版本",
  "workflowHistory.action.clone.success.message": "在新标签页中打开克隆的工作流",
  "workflowHistory.button.tooltip.empty": "此工作流当前没有历史记录可供查看。完成第一次保存后，您将可以查看以前的版本",
  "workflowHistory.button.tooltip.enabled": "用于查看和恢复工作流以前版本的历史记录",
  "workflowHistory.button.tooltip.disabled": "升级以解锁工作流历史记录，以查看和恢复以前的工作流版本。{link}",
  "workflowHistory.button.tooltip.disabled.link": "查看计划",
  "workflows.heading": "工作流",
  "workflows.add": "添加工作流",
  "workflows.project.add": "将工作流添加到项目",
  "workflows.item.open": "打开",
  "workflows.item.share": "共享...",
  "workflows.item.duplicate": "复制",
  "workflows.item.delete": "删除",
  "workflows.item.archive": "存档",
  "workflows.item.unarchive": "解档",
  "workflows.item.move": "移动",
  "workflows.item.changeOwner": "更改所有者",
  "workflows.item.updated": "修改于",
  "workflows.item.created": "创建于",
  "workflows.item.readonly": "只读",
  "workflows.item.archived": "已存档",
  "workflows.search.placeholder": "搜索工作流...",
  "workflows.filters": "筛选器",
  "workflows.filters.tags": "标签",
  "workflows.filters.status": "状态",
  "workflows.filters.status.all": "全部",
  "workflows.filters.status.active": "活动",
  "workflows.filters.status.deactivated": "已停用",
  "workflows.filters.showArchived": "显示已归档的工作流程",
  "workflows.filters.ownedBy": "所有者",
  "workflows.filters.sharedWith": "共享对象",
  "workflows.filters.apply": "应用筛选器",
  "workflows.filters.reset": "重置全部",
  "workflows.filters.active": "应用筛选器后，某些工作流可能会被隐藏。",
  "workflows.filters.active.shortText": "已应用筛选条件。",
  "workflows.filters.active.reset": "移除筛选器",
  "workflows.sort.lastUpdated": "按上次更新时间排序",
  "workflows.sort.lastCreated": "按上次创建时间排序",
  "workflows.sort.nameAsc": "按名称排序 (A-Z)",
  "workflows.sort.nameDesc": "按名称排序 (Z-A)",
  "workflows.noResults": "未找到任何工作流",
  "workflows.noResults.withSearch.switchToShared.preamble": "某些工作流可能",
  "workflows.noResults.withSearch.switchToShared.link": "已隐藏",
  "workflows.empty.heading": "👋 欢迎 {name}！",
  "workflows.empty.heading.userNotSetup": "👋 欢迎！",
  "workflows.empty.description": "创建您的第一个工作流",
  "workflows.empty.description.readOnlyEnv": "此处尚无任何工作流",
  "workflows.empty.description.noPermission": "当前没有要查看的工作流",
  "workflows.empty.startFromScratch": "从头开始",
  "workflows.empty.startWithTemplate": "从模板开始",
  "workflows.empty.browseTemplates": "浏览工作流模板",
  "workflows.empty.learnN8n": "了解 n8n",
  "workflows.empty.button.disabled.tooltip": "您在项目中的当前角色不允许您创建工作流",
  "workflows.empty.easyAI": "测试一个简单的AI代理示例",
  "workflows.empty.shared-with-me": "没有与您共享任何 {resource}",
  "workflows.empty.shared-with-me.link": '<a href="#">返回个人主页</a>',
  "workflows.list.easyAI": "用这个简单的AI代理工作流，在n8n中测试人工智能的力量",
  "workflows.list.error.fetching": "获取工作流时出错",
  "workflows.shareModal.title": "共享 '{name}'",
  "workflows.shareModal.title.static": "已与 {projectName} 共享",
  "workflows.shareModal.select.placeholder": "添加用户...",
  "workflows.shareModal.list.delete": "移除访问权限",
  "workflows.shareModal.list.delete.confirm.title": "移除 {name} 的访问权限？",
  "workflows.shareModal.list.delete.confirm.lastUserWithAccessToCredentials.message": "如果这样做，工作流将失去对 {name} 的凭据的访问权限。<strong>使用这些凭据的节点将停止工作</strong>。",
  "workflows.shareModal.list.delete.confirm.confirmButtonText": "移除访问权限",
  "workflows.shareModal.list.delete.confirm.cancelButtonText": "取消",
  "workflows.shareModal.onSave.success.title": "共享已更新",
  "workflows.shareModal.onSave.error.title": "保存共享设置时出现问题",
  "workflows.shareModal.saveBeforeClose.title": "保存共享更改？",
  "workflows.shareModal.saveBeforeClose.message": "您有未保存的更改。您要先保存它们再关闭吗？",
  "workflows.shareModal.saveBeforeClose.confirmButtonText": "保存",
  "workflows.shareModal.saveBeforeClose.cancelButtonText": "关闭而不保存",
  "workflows.shareModal.save": "保存",
  "workflows.shareModal.changesHint": "您进行了更改",
  "workflows.shareModal.info.sharee": "只有 {workflowOwnerName} 或具有工作流共享权限的用户才能更改此工作流的共享对象",
  "workflows.shareModal.info.sharee.fallback": "所有者",
  "workflows.shareModal.info.members": "此工作流由 {projectName} 项目拥有，该项目当前有 {members} 可以访问此工作流。",
  "workflows.shareModal.info.members.number": "{number} 个成员 | {number} 个成员",
  "workflows.shareModal.role.editor": "编辑者",
  "workflows.roles.editor": "编辑者",
  "workflows.concurrentChanges.confirmMessage.title": "工作流已被其他人更改",
  "workflows.concurrentChanges.confirmMessage.message": '在您编辑此工作流时，有人保存了它。您可以<a href="{url}" target="_blank">查看他们的版本</a>（在新标签页中）。<br/><br/>用您的更改覆盖他们的更改？',
  "workflows.concurrentChanges.confirmMessage.cancelButtonText": "取消",
  "workflows.concurrentChanges.confirmMessage.confirmButtonText": "覆盖并保存",
  "workflows.create.personal.toast.title": "工作流已成功创建",
  "workflows.create.personal.toast.text": "此工作流已在您的个人空间中创建。",
  "workflows.create.project.toast.title": "工作流已在 {projectName} 中成功创建",
  "workflows.create.folder.toast.title": "工作流已在“{projectName}”项目中的“{folderName}”文件夹内成功创建",
  "workflows.create.project.toast.text": "{projectName} 中的所有成员都将可以访问此工作流。",
  "workflows.deactivated": "工作流已停用",
  "workflowSelectorParameterInput.createNewSubworkflow.name": "我的子工作流程",
  "importCurlModal.title": "导入 cURL 命令",
  "importCurlModal.input.label": "cURL 命令",
  "importCurlModal.input.placeholder": "在此处粘贴 cURL 命令",
  "ImportCurlModal.notice.content": "这将覆盖您已对当前节点所做的任何更改",
  "importCurlModal.button.label": "导入",
  "importCurlParameter.label": "导入 cURL",
  "importCurlParameter.showError.invalidCurlCommand.title": "无法导入 cURL 命令",
  "importCurlParameter.showError.invalidCurlCommand.message": "此命令的格式不受支持",
  "importCurlParameter.showError.invalidProtocol1.title": "使用 {node} 节点",
  "importCurlParameter.showError.invalidProtocol2.title": "协议无效",
  "importCurlParameter.showError.invalidProtocol.message": "HTTP 节点不支持 {protocol} 请求",
  "variables.heading": "变量",
  "variables.add": "添加变量",
  "variables.add.unavailable": "升级计划以继续使用变量",
  "variables.add.unavailable.empty": "升级计划以开始使用变量",
  "variables.add.onlyOwnerCanCreate": "只有所有者才能创建变量",
  "variables.empty.heading": "{name}，让我们设置一个变量",
  "variables.empty.heading.userNotSetup": "设置变量",
  "variables.empty.description": "可以使用变量来存储可在多个工作流中轻松引用的数据。",
  "variables.empty.button": "添加第一个变量",
  "variables.empty.button.disabled.tooltip": "您在项目中的当前角色不允许您创建变量",
  "variables.empty.notAllowedToCreate.heading": "{name}，开始使用变量",
  "variables.empty.notAllowedToCreate.description": "让您的 n8n 实例所有者创建您需要的变量。配置完成后，您可以使用语法 $vars.MY_VAR 在您的工作流中利用它们。",
  "variables.filters.active": "由于已应用筛选条件，部分变量可能被隐藏。",
  "variables.filters.active.reset": "清除筛选条件",
  "variables.noResults": "未找到任何变量",
  "variables.sort.nameAsc": "按名称排序 (A-Z)",
  "variables.sort.nameDesc": "按名称排序 (Z-A)",
  "variables.table.key": "键",
  "variables.table.value": "值",
  "variables.table.usage": "使用语法",
  "variables.editing.key.placeholder": "输入名称",
  "variables.editing.value.placeholder": "输入值",
  "variables.editing.key.error.startsWithLetter": "此字段只能以字母开头",
  "variables.editing.key.error.jsonKey": "此字段只能包含字母、数字和下划线",
  "variables.row.button.save": "保存",
  "variables.row.button.cancel": "取消",
  "variables.row.button.edit": "编辑",
  "variables.row.button.edit.onlyRoleCanEdit": "只有实例所有者和管理员才能编辑变量",
  "variables.row.button.delete": "删除",
  "variables.row.button.delete.onlyRoleCanDelete": "只有实例所有者和才能删除变量",
  "variables.row.usage.copiedToClipboard": "已复制到剪贴板",
  "variables.row.usage.copyToClipboard": "复制到剪贴板",
  "variables.search.placeholder": "搜索变量...",
  "variables.errors.save": "保存变量时出错",
  "variables.errors.delete": "删除变量时出错",
  "variables.modals.deleteConfirm.title": "删除变量",
  "variables.modals.deleteConfirm.message": "您确定要删除变量“{name}”吗？此操作无法撤消。",
  "variables.modals.deleteConfirm.confirmButton": "删除",
  "variables.modals.deleteConfirm.cancelButton": "取消",
  "contextual.credentials.sharing.unavailable.title": "升级以进行协作",
  "contextual.credentials.sharing.unavailable.title.cloud": "升级以进行协作",
  "contextual.credentials.sharing.unavailable.description": "升级您的计划后，您可以与他人共享凭据。",
  "contextual.credentials.sharing.unavailable.description.cloud": "升级您的计划后，您可以与他人共享凭据。",
  "contextual.credentials.sharing.unavailable.button": "查看计划",
  "contextual.credentials.sharing.unavailable.button.cloud": "立即升级",
  "contextual.workflows.sharing.title": "共享",
  "contextual.workflows.sharing.unavailable.title": "共享",
  "contextual.workflows.sharing.unavailable.title.cloud": "升级以进行协作",
  "contextual.workflows.sharing.unavailable.description.modal": "升级您的计划后，您可以与其他人协作处理工作流。",
  "contextual.workflows.sharing.unavailable.description.modal.cloud": "升级您的计划后，您可以与其他人协作处理工作流。",
  "contextual.workflows.sharing.unavailable.description.tooltip": "升级您的计划后，您可以与其他人协作处理工作流。{action}",
  "contextual.workflows.sharing.unavailable.description.tooltip.cloud": "升级您的计划后，您可以与其他人协作处理工作流。{action}",
  "contextual.workflows.sharing.unavailable.button": "查看计划",
  "contextual.workflows.sharing.unavailable.button.cloud": "立即升级",
  "contextual.variables.unavailable.title": "在企业版计划中可用",
  "contextual.variables.unavailable.title.cloud": "在 Pro 计划中可用",
  "contextual.variables.unavailable.description": '变量可用于存储和访问跨工作流的数据。在 n8n 中使用前缀 <code>$vars</code> 引用它们（例如，<code>$vars.myVariable</code>）。变量是不可变的，无法在您的工作流中修改。<br/><a href="https://docs.n8n.io/environments/variables/" target="_blank">在文档中了解更多信息。</a>',
  "contextual.variables.unavailable.button": "查看计划",
  "contextual.variables.unavailable.button.cloud": "立即升级",
  "contextual.users.settings.unavailable.title": "升级以添加用户",
  "contextual.users.settings.unavailable.title.cloud": "升级以添加用户",
  "contextual.users.settings.unavailable.description": "在更高版本的计划中创建多个用户，并共享工作流和凭据以进行协作",
  "contextual.users.settings.unavailable.description.cloud": "在更高版本的计划中创建多个用户，并共享工作流和凭据以进行协作",
  "contextual.users.settings.unavailable.button": "查看计划",
  "contextual.users.settings.unavailable.button.cloud": "立即升级",
  "contextual.feature.unavailable.title": "在企业版计划中可用",
  "contextual.feature.unavailable.title.cloud": "在 Pro 计划中可用",
  "settings.ldap": "LDAP",
  "settings.ldap.note": "LDAP 允许用户使用其集中式帐户进行身份验证。它与提供 LDAP 接口的服务（如 Active Directory、Okta 和 Jumpcloud）兼容。",
  "settings.ldap.infoTip": "在文档中了解有关 <a href='https://docs.n8n.io/user-management/ldap/' target='_blank'>LDAP 的更多信息</a>",
  "settings.ldap.save": "保存连接",
  "settings.ldap.connectionTestError": "测试 LDAP 连接时出现问题",
  "settings.ldap.connectionTest": "LDAP 连接已测试",
  "settings.ldap.runSync.title": "LDAP 同步已完成",
  "settings.ldap.runSync.showError.message": "同步期间出现问题。请检查日志",
  "settings.ldap.updateConfiguration": "LDAP 配置已更新",
  "settings.ldap.testingConnection": "正在测试连接",
  "settings.ldap.testConnection": "测试连接",
  "settings.ldap.synchronizationTable.column.status": "状态",
  "settings.ldap.synchronizationTable.column.endedAt": "结束时间",
  "settings.ldap.synchronizationTable.column.runMode": "运行模式",
  "settings.ldap.synchronizationTable.column.runTime": "运行时间",
  "settings.ldap.synchronizationTable.column.details": "详细信息",
  "settings.ldap.synchronizationTable.empty.message": "测试同步以预览更新",
  "settings.ldap.dryRun": "测试同步",
  "settings.ldap.synchronizeNow": "立即运行同步",
  "settings.ldap.synchronizationError": "LDAP 同步错误",
  "settings.ldap.configurationError": "LDAP 配置错误",
  "settings.ldap.usersScanned": "扫描的用户 {scanned}",
  "settings.ldap.confirmMessage.beforeSaveForm.cancelButtonText": "否",
  "settings.ldap.confirmMessage.beforeSaveForm.confirmButtonText": "是，禁用它",
  "settings.ldap.confirmMessage.beforeSaveForm.headline": "您确定要禁用 LDAP 登录吗？",
  "settings.ldap.confirmMessage.beforeSaveForm.message": "如果这样做，所有 LDAP 用户都将转换为电子邮件用户。",
  "settings.ldap.disabled.title": "在企业版计划中可用",
  "settings.ldap.disabled.description": "LDAP 作为付费功能提供。了解有关它的更多信息。",
  "settings.ldap.disabled.buttonText": "查看计划",
  "settings.ldap.toast.sync.success": "同步成功",
  "settings.ldap.toast.connection.success": "连接成功",
  "settings.ldap.form.loginEnabled.label": "启用 LDAP 登录",
  "settings.ldap.form.loginEnabled.tooltip": "如果禁用 LDAP 登录，连接设置和数据仍将保存",
  "settings.ldap.form.loginLabel.label": "LDAP 登录",
  "settings.ldap.form.loginLabel.placeholder": "例如：LDAP 用户名或电子邮件地址",
  "settings.ldap.form.loginLabel.infoText": "登录页面的登录字段中显示的占位符文本",
  "settings.ldap.form.serverAddress.label": "LDAP 服务器地址",
  "settings.ldap.form.serverAddress.placeholder": "***************",
  "settings.ldap.form.serverAddress.infoText": "LDAP 服务器的 IP 或域",
  "settings.ldap.form.port.label": "LDAP 服务器端口",
  "settings.ldap.form.port.infoText": "用于连接到 LDAP 服务器的端口",
  "settings.ldap.form.connectionSecurity.label": "连接安全性",
  "settings.ldap.form.connectionSecurity.infoText": "连接安全类型",
  "settings.ldap.form.allowUnauthorizedCerts.label": "忽略 SSL/TLS 问题",
  "settings.ldap.form.baseDn.label": "Base DN",
  "settings.ldap.form.baseDn.placeholder": "o=acme,dc=example,dc=com",
  "settings.ldap.form.baseDn.infoText": "n8n 应在 AD/LDAP 树中开始搜索用户的位置的专有名称",
  "settings.ldap.form.bindingType.label": "绑定为",
  "settings.ldap.form.bindingType.infoText": "用于连接到 LDAP 服务器的绑定类型",
  "settings.ldap.form.adminDn.label": "绑定 DN",
  "settings.ldap.form.adminDn.placeholder": "uid=2da2de69435c,ou=Users,o=Acme,dc=com",
  "settings.ldap.form.adminDn.infoText": "执行搜索的用户的专有名称",
  "settings.ldap.form.adminPassword.label": "绑定密码",
  "settings.ldap.form.adminPassword.infoText": "在上述绑定 DN 字段中提供的用户的密码",
  "settings.ldap.form.userFilter.label": "用户筛选器",
  "settings.ldap.form.userFilter.placeholder": "(ObjectClass=user)",
  "settings.ldap.form.userFilter.infoText": "搜索用户时要使用的 LDAP 查询。只有此筛选器返回的用户才被允许登录 n8n",
  "settings.ldap.form.attributeMappingInfo.label": "属性映射",
  "settings.ldap.form.ldapId.label": "ID",
  "settings.ldap.form.ldapId.placeholder": "uid",
  "settings.ldap.form.ldapId.infoText": "LDAP 服务器中用作 n8n 中唯一标识符的属性。它应该是一个唯一的 LDAP 属性，如 uid",
  "settings.ldap.form.loginId.label": "登录 ID",
  "settings.ldap.form.loginId.placeholder": "mail",
  "settings.ldap.form.loginId.infoText": "LDAP 服务器中用于在 n8n 中登录的属性",
  "settings.ldap.form.email.label": "电子邮件",
  "settings.ldap.form.email.placeholder": "mail",
  "settings.ldap.form.email.infoText": "LDAP 服务器中用于填充 n8n 中的电子邮件的属性",
  "settings.ldap.form.firstName.label": "名字",
  "settings.ldap.form.firstName.placeholder": "givenName",
  "settings.ldap.form.firstName.infoText": "LDAP 服务器中用于填充 n8n 中的名字的属性",
  "settings.ldap.form.lastName.label": "姓氏",
  "settings.ldap.form.lastName.placeholder": "sn",
  "settings.ldap.form.lastName.infoText": "LDAP 服务器中用于填充 n8n 中的姓氏的属性",
  "settings.ldap.form.synchronizationEnabled.label": "启用定期 LDAP 同步",
  "settings.ldap.form.synchronizationEnabled.tooltip": "允许定期同步用户",
  "settings.ldap.form.synchronizationInterval.label": "同步间隔（分钟）",
  "settings.ldap.form.synchronizationInterval.infoText": "同步应运行的频率",
  "settings.ldap.form.pageSize.label": "页面大小",
  "settings.ldap.form.pageSize.infoText": "同步期间每页返回的最大记录数。0 表示不限制",
  "settings.ldap.form.searchTimeout.label": "搜索超时（秒）",
  "settings.ldap.form.searchTimeout.infoText": "对 AD/LDAP 服务器的查询的超时值。如果您因 AD/LDAP 服务器速度慢而收到超时错误，请增加此值",
  "settings.ldap.section.synchronization.title": "同步",
  "settings.sso": "SSO",
  "settings.sso.title": "单点登录",
  "settings.sso.subtitle": "SAML 2.0 配置",
  "settings.sso.info": "激活 SAML SSO 以通过您现有的用户管理工具启用无密码登录，并通过统一身份验证增强安全性。",
  "settings.sso.info.link": "了解如何配置 SAML 2.0。",
  "settings.sso.activation.tooltip": "您需要先保存设置，然后再激活 SAML",
  "settings.sso.activated": "已激活",
  "settings.sso.deactivated": "已停用",
  "settings.sso.settings.redirectUrl.label": "重定向 URL",
  "settings.sso.settings.redirectUrl.copied": "重定向 URL 已复制到剪贴板",
  "settings.sso.settings.redirectUrl.help": "复制重定向 URL 以配置您的 SAML 提供商",
  "settings.sso.settings.entityId.label": "实体 ID",
  "settings.sso.settings.entityId.copied": "实体 ID 已复制到剪贴板",
  "settings.sso.settings.entityId.help": "复制实体 ID URL 以配置您的 SAML 提供商",
  "settings.sso.settings.ips.label": "身份提供商设置",
  "settings.sso.settings.ips.xml.help": "在此处粘贴您的身份提供商提供的原始元数据 XML",
  "settings.sso.settings.ips.url.help": "在此处粘贴身份提供商元数据URL",
  "settings.sso.settings.ips.url.placeholder": "例如：https://samltest.id/saml/idp",
  "settings.sso.settings.ips.url.invalid": "身份提供者元数据URL无效",
  "settings.sso.settings.ips.options.url": "元数据 URL",
  "settings.sso.settings.ips.options.xml": "XML",
  "settings.sso.settings.test": "测试设置",
  "settings.sso.settings.save": "保存设置",
  "settings.sso.settings.save.activate.title": "测试并激活 SAML SSO",
  "settings.sso.settings.save.activate.message": "SAML SSO 配置已成功保存。首先测试您的 SAML SSO 设置，然后激活以为您的组织启用单点登录。",
  "settings.sso.settings.save.activate.cancel": "取消",
  "settings.sso.settings.save.activate.test": "测试设置",
  "settings.sso.settings.save.error": "保存 SAML SSO 配置时出错",
  "settings.sso.settings.footer.hint": "保存设置后，请不要忘记激活 SAML SSO。",
  "settings.sso.actionBox.title": "在企业版计划中可用",
  "settings.sso.actionBox.description": "使用单点登录将身份验证整合到单个平台中，以提高安全性和敏捷性。",
  "settings.sso.actionBox.buttonText": "查看计划",
  "settings.oidc.confirmMessage.beforeSaveForm.headline": "您确定要禁用 OIDC 登录吗？",
  "settings.oidc.confirmMessage.beforeSaveForm.message": "如果这样做，所有 OIDC 用户都将被转换为邮箱用户。",
  "settings.mfa.secret": "密钥 {secret}",
  "settings.mfa": "MFA",
  "settings.mfa.title": "多因素身份验证",
  "settings.mfa.updateConfiguration": "MFA 配置已更新",
  "settings.mfa.invalidAuthenticatorCode": "验证器代码无效",
  "projects.header.overview.subtitle": "您有权访问的所有工作流程、凭证及执行记录",
  "projects.header.shared.title": "与你分享",
  "projects.header.personal.subtitle": "您拥有的工作流程和凭证",
  "projects.header.shared.subtitle": "其他用户与你共享的工作流程和凭证",
  "projects.header.create.workflow": "创建工作流程",
  "projects.header.create.credential": "创建凭证",
  "projects.header.create.folder": "创建文件夹",
  "projects.create": "创建",
  "projects.create.personal": "在个人中创建",
  "projects.create.team": "在项目中创建",
  "projects.menu.overview": "概述",
  "projects.menu.shared": "分享给你",
  "projects.menu.title": "项目",
  "projects.menu.personal": "个人",
  "projects.menu.addFirstProject": "添加第一个项目",
  "projects.settings": "项目设置",
  "projects.settings.newProjectName": "我的项目",
  "projects.settings.iconPicker.button.tooltip": "选择项目图标",
  "projects.settings.name": "项目图标和名称",
  "projects.settings.description": "项目描述",
  "projects.settings.projectMembers": "项目成员",
  "projects.settings.message.unsavedChanges": "您有未保存的更改",
  "projects.settings.danger.message": "删除项目时，您还可以选择将所有工作流和凭据移动到另一个项目。",
  "projects.settings.danger.title": "危险区域",
  "projects.settings.danger.deleteProject": "删除此项目",
  "projects.settings.button.save": "@:_reusableBaseText.save",
  "projects.settings.button.cancel": "@:_reusableBaseText.cancel",
  "projects.settings.button.deleteProject": "删除项目",
  "projects.settings.role.admin": "管理员",
  "projects.settings.role.editor": "编辑者",
  "projects.settings.role.viewer": "查看者",
  "projects.settings.delete.title": "删除 {projectName}",
  "projects.settings.delete.message": "我们应该如何处理项目数据？",
  "projects.settings.delete.message.empty": "这个项目中没有工作流或凭证。",
  "projects.settings.delete.question.transfer.label": "将其工作流和凭据转移到另一个项目或用户",
  "projects.settings.delete.question.transfer.title": "要转移到的项目或用户",
  "projects.settings.delete.question.wipe.label": "删除其工作流和凭据",
  "projects.settings.delete.question.wipe.title": "键入“删除所有数据”以确认",
  "projects.settings.delete.question.wipe.placeholder": "删除所有数据",
  "projects.settings.delete.confirm": "是的，我确定",
  "projects.settings.delete.cancel": "否，取消",
  "projects.settings.delete.successful.title": "已删除项目 {projectName}",
  "projects.settings.delete.error.title": "删除项目时发生错误",
  "projects.settings.save.successful.title": "项目 {projectName} 已成功保存",
  "projects.settings.icon.update.successful.title": "项目图标已成功更新",
  "projects.settings.save.error.title": "保存项目时发生错误",
  "projects.settings.role.upgrade.title": "升级以解锁其他角色",
  "projects.settings.role.upgrade.message": "您目前在 {planName} 计划中仅限于 {limit} 个角色，并且只能在此项目中向用户分配管理员角色。要创建更多项目并解锁其他角色，请升级您的计划。",
  "projects.sharing.noMatchingProjects": "没有可用的项目",
  "projects.sharing.noMatchingUsers": "没有匹配的用户或项目",
  "projects.sharing.select.placeholder": "选择项目或用户",
  "projects.sharing.select.placeholder.user": "与用户共享",
  "projects.sharing.select.placeholder.project": "与项目或用户共享",
  "projects.error.title": "项目错误",
  "projects.create.limit": "{num} 个项目 | {num} 个项目",
  "projects.create.limitReached": "您已达到 {planName} 计划的 {limit} 限制。升级您的计划以解锁更多项目。{link}",
  "projects.create.limitReached.cloud": "您已达到 {planName} 计划的 {limit} 限制。升级您的计划以解锁更多项目。",
  "projects.create.limitReached.self": "升级以解锁项目，从而更精细地控制工作流的共享、访问和组织",
  "projects.create.limitReached.link": "查看计划",
  "projects.create.permissionDenied": "您当前的权限不允许创建项目",
  "projects.move.resource.modal.title": "选择一个项目或用户以将此 {resourceTypeLabel} 移动到",
  "projects.move.resource.modal.message": "“{resourceName}”当前{inPersonalProject}{inTeamProject}",
  "projects.move.resource.modal.message.team": "位于“{resourceHomeProjectName}”项目中。",
  "projects.move.resource.modal.message.personal": "由“{resourceHomeProjectName}”拥有。",
  "projects.move.resource.modal.message.note": "备注",
  "projects.move.resource.modal.message.sharingNote": "{note}：移动将删除此 {resourceTypeLabel} 的任何现有共享。",
  "projects.move.resource.modal.message.sharingInfo": "（当前与 {numberOfProjects} 个项目共享）|（当前与 {numberOfProjects} 个项目共享）",
  "projects.move.resource.modal.message.usedCredentials": "同时请分享此工作流所使用的{usedCredentials}，以确保其能够持续正常运行。",
  "projects.move.resource.modal.message.usedCredentials.number": "{count} 个凭证 | {count} 个凭证",
  "projects.move.resource.modal.message.unAccessibleCredentials": "一些凭证",
  "projects.move.resource.modal.message.unAccessibleCredentials.note": "此工作流中使用的{credentials}将不会被共享",
  "projects.move.resource.modal.message.noProjects": "当前没有任何项目或用户可供您将此 {resourceTypeLabel} 移动到。",
  "projects.move.resource.modal.button": "移动 {resourceTypeLabel}",
  "projects.move.resource.modal.selectPlaceholder": "选择项目或用户...",
  "projects.move.resource.error.title": "移动 {resourceName} {resourceTypeLabel} 时出错",
  "projects.move.resource.success.title": "{resourceTypeLabel} 已成功移动",
  "projects.move.resource.success.message.workflow": "请仔细检查此工作流使用的任何凭据是否也与 {targetProjectName} 共享。",
  "projects.move.resource.success.message.workflow.withAllCredentials": "该工作流的凭证已与项目共享。",
  "projects.move.resource.success.message.workflow.withSomeCredentials": "由于权限不足，未能将该工作流程的所有凭证共享至项目。",
  "projects.move.resource.success.link": "查看 {targetProjectName}",
  "projects.badge.tooltip.sharedOwned": "此 {resourceTypeLabel} 由您拥有并与 {count} 个用户共享",
  "projects.badge.tooltip.sharedPersonal": "此 {resourceTypeLabel} 由 {name} 拥有并与 {count} 个用户共享",
  "projects.badge.tooltip.personal": "此 {resourceTypeLabel} 由 {name} 拥有",
  "projects.badge.tooltip.team": "此 {resourceTypeLabel} 由 {name} 项目拥有并可访问。",
  "projects.badge.tooltip.sharedTeam": "此 {resourceTypeLabel} 由 {name} 项目拥有，并且可由 {count} 个用户访问",
  "mfa.setup.invalidAuthenticatorCode": "{code} 不是有效的数字",
  "mfa.setup.invalidCode": "双因素代码失败。请重试。",
  "mfa.code.modal.title": "双因素身份验证",
  "mfa.recovery.modal.title": "双因素恢复",
  "mfa.code.input.info": "没有您的身份验证设备？",
  "mfa.code.input.info.action": "输入恢复代码",
  "mfa.recovery.input.info.action": "输入恢复代码",
  "mfa.code.button.continue": "继续",
  "mfa.recovery.button.verify": "验证",
  "mfa.button.back": "返回",
  "mfa.code.input.label": "双因素代码",
  "mfa.code.input.placeholder": "例如：123456",
  "mfa.code.recovery.input.label": "双因素代码或恢复代码",
  "mfa.code.recovery.input.placeholder": "例如：123456 或 c79f9c02-7b2e-44...",
  "mfa.recovery.input.label": "恢复代码",
  "mfa.recovery.input.placeholder": "例如：c79f9c02-7b2e-44...",
  "mfa.code.invalid": "此代码无效，请重试或",
  "mfa.recovery.invalid": "此代码无效或已使用，请重试",
  "mfa.setup.step1.title": "设置验证器应用 [1/2]",
  "mfa.setup.step2.title": "下载您的恢复代码 [2/2]",
  "mfa.setup.step1.instruction1.title": "1. 扫描二维码",
  "mfa.setup.step1.instruction1.subtitle": "{part1} {part2}",
  "mfa.setup.step1.instruction1.subtitle.part1": "使用手机中的验证器应用进行扫描。如果您无法扫描二维码，请输入",
  "mfa.setup.step1.instruction1.subtitle.part2": "此文本代码",
  "mfa.setup.step1.instruction2.title": "2. 输入应用中的代码",
  "mfa.setup.step2.description": "如果无法访问您的设备，则可以使用恢复代码作为辅助身份验证因素。",
  "mfa.setup.step2.infobox.description": "{part1} {part2}",
  "mfa.setup.step2.infobox.description.part1": "请将恢复代码保存在安全的地方。如果您丢失了您的设备和恢复代码，您将",
  "mfa.setup.step2.infobox.description.part2": "无法访问您的帐户。",
  "mfa.setup.step2.button.download": "下载恢复代码",
  "mfa.setup.step2.button.save": "我已下载我的恢复代码",
  "mfa.setup.step1.button.continue": "继续",
  "mfa.setup.step1.input.label": "来自验证器应用的代码",
  "mfa.setup.step1.toast.copyToClipboard.title": "代码已复制到剪贴板",
  "mfa.setup.step1.toast.copyToClipboard.message": "在您的验证器应用中输入代码",
  "mfa.setup.step2.toast.setupFinished.message": "已启用双因素身份验证",
  "mfa.setup.step2.toast.setupFinished.error.message": "启用双因素身份验证时出错",
  "mfa.setup.step2.toast.tokenExpired.error.message": "MFA 令牌已过期。关闭模式，然后再次启用 MFA",
  "mfa.prompt.code.modal.title": "需要双因素代码或恢复代码",
  "settings.personal.mfa.section.title": "双因素身份验证 (2FA)",
  "settings.personal.personalisation": "个性化",
  "settings.personal.theme": "主题",
  "settings.personal.theme.systemDefault": "系统默认",
  "settings.personal.theme.light": "浅色主题",
  "settings.personal.theme.dark": "深色主题",
  "settings.personal.mfa.button.disabled.infobox": "双因素身份验证当前已禁用。",
  "settings.personal.mfa.button.enabled.infobox": "双因素身份验证当前已启用。",
  "settings.personal.mfa.button.enabled": "启用 2FA",
  "settings.personal.mfa.button.disabled": "禁用双因素身份验证",
  "settings.personal.mfa.toast.disabledMfa.title": "双因素身份验证已禁用",
  "settings.personal.mfa.toast.disabledMfa.message": "您将不再需要在登录时使用验证器应用",
  "settings.personal.mfa.toast.disabledMfa.error.message": "禁用双因素身份验证时出错",
  "settings.personal.mfa.toast.canEnableMfa.title": "MFA 先决条件失败",
  "settings.personal.mfa.enforced": "此实例的设置**要求您设置双重身份验证**。请启用它以继续在此实例中工作。",
  "settings.personal.mfa.enforce.message": "为该实例的所有用户强制启用双因素身份验证。",
  "settings.personal.mfa.enforce.unlicensed_tooltip": "升级您的套餐后，您可以为该实例的所有用户强制启用双重身份验证。{action}",
  "settings.personal.mfa.enforce.unlicensed_tooltip.link": "查看方案",
  "settings.personal.mfa.enforce.title": "强制双因素身份验证",
  "settings.personal.mfa.enforce.error": "无法强制所有用户启用双重认证。",
  "settings.personal.mfa.enforce.enabled.title": "双重认证已启用",
  "settings.personal.mfa.enforce.enabled.message": "现在，此实例上的所有用户都需要启用双重身份验证。",
  "settings.personal.mfa.enforce.disabled.title": "双因素身份验证不再强制启用",
  "settings.personal.mfa.enforce.disabled.message": "双因素身份验证对于此实例的用户来说不再是强制性的。",
  "settings.mfa.toast.noRecoveryCodeLeft.title": "没有剩余的 2FA 恢复代码",
  "settings.mfa.toast.noRecoveryCodeLeft.message": "您已使用所有恢复代码。禁用然后重新启用双因素身份验证以生成新代码。<a href='/settings/personal' target='_blank' >打开设置</a>",
  "sso.login.divider": "或",
  "sso.login.button": "继续使用 SSO",
  "executionUsage.currentUsage": "{text} {count}",
  "executionUsage.currentUsage.text": "您正在免费试用，执行次数有限。您有",
  "executionUsage.currentUsage.count": "{n} 天剩余。| {n} 天剩余。",
  "executionUsage.label.executions": "执行",
  "executionUsage.button.upgrade": "升级计划",
  "executionUsage.expired.text": "您的试用已结束。立即升级以保留您的数据。",
  "executionUsage.ranOutOfExecutions.text": "您的执行次数已用完。升级您的计划以继续自动化。",
  "openExecution.missingExeuctionId.title": "找不到执行",
  "openExecution.missingExeuctionId.message": "请确保此工作流通过设置保存执行",
  "type.string": "字符串",
  "type.number": "数字",
  "type.dateTime": "日期和时间",
  "type.boolean": "布尔值",
  "type.array": "数组",
  "type.object": "对象",
  "filter.operator.equals": "等于",
  "filter.operator.notEquals": "不等于",
  "filter.operator.contains": "包含",
  "filter.operator.notContains": "不包含",
  "filter.operator.startsWith": "以开头",
  "filter.operator.notStartsWith": "不以开头",
  "filter.operator.endsWith": "以结尾",
  "filter.operator.notEndsWith": "不以结尾",
  "filter.operator.exists": "存在",
  "filter.operator.notExists": "不存在",
  "filter.operator.regex": "匹配正则表达式",
  "filter.operator.notRegex": "不匹配正则表达式",
  "filter.operator.gt": "大于",
  "filter.operator.lt": "小于",
  "filter.operator.gte": "大于或等于",
  "filter.operator.lte": "小于或等于",
  "filter.operator.after": "在之后",
  "filter.operator.before": "在之前",
  "filter.operator.afterOrEquals": "在之后或等于",
  "filter.operator.beforeOrEquals": "在之前或等于",
  "filter.operator.true": "为真",
  "filter.operator.false": "为假",
  "filter.operator.lengthEquals": "长度等于",
  "filter.operator.lengthNotEquals": "长度不等于",
  "filter.operator.lengthGt": "长度大于",
  "filter.operator.lengthLt": "长度小于",
  "filter.operator.lengthGte": "长度大于或等于",
  "filter.operator.lengthLte": "长度小于或等于",
  "filter.operator.empty": "为空",
  "filter.operator.notEmpty": "不为空",
  "filter.combinator.or": "或",
  "filter.combinator.and": "与",
  "filter.addCondition": "添加条件",
  "filter.removeCondition": "删除条件",
  "filter.dragCondition": "拖动条件",
  "filter.maxConditions": "已达到最大条件数",
  "filter.condition.resolvedTrue": "此条件对于第一项输入项为真",
  "filter.condition.resolvedFalse": "此条件对于第一项输入项为假",
  "filter.condition.placeholderLeft": "值1",
  "filter.condition.placeholderRight": "值2",
  "assignment.dragFields": "在此处拖动输入字段",
  "assignment.dropField": "拖放到此处以添加字段",
  "assignment.or": "或",
  "assignment.add": "添加字段",
  "assignment.addAll": "添加全部",
  "assignment.clearAll": "清除全部",
  "templateSetup.title": "设置“{name}”模板",
  "templateSetup.instructions": "您需要 {0} 个帐户才能设置此模板",
  "templateSetup.skip": "跳过",
  "templateSetup.continue.button": "继续",
  "templateSetup.credential.description": "您选择的凭据将用于工作流模板的 {0} 节点中。| 您选择的凭据将用于工作流模板的 {0} 个节点中。",
  "templateSetup.continue.button.fillRemaining": "填写剩余凭据以继续",
  "setupCredentialsModal.title": "设置模板",
  "becomeCreator.text": "与 40,000 多名用户共享您的工作流，解锁特权，并成为精选模板创建者！",
  "becomeCreator.buttonText": "成为创建者",
  "becomeCreator.closeButtonTitle": "关闭",
  "feedback.title": "这是否有帮助？",
  "feedback.positive": "我觉得这有帮助",
  "feedback.negative": "我觉得这没有帮助",
  "communityPlusModal.badge": "限时优惠",
  "communityPlusModal.title": "免费获得付费功能（永久）",
  "communityPlusModal.error.title": "许可证请求失败",
  "communityPlusModal.success.title": "请求已发送",
  "communityPlusModal.success.message": "许可证密钥将发送至 {email}",
  "communityPlusModal.description": "接收用于以下高级功能的免费激活密钥 - 终身访问权限。",
  "communityPlusModal.features.first.title": "工作流历史记录",
  "communityPlusModal.features.first.description": "查看和恢复过去 24 小时内的任何工作流版本",
  "communityPlusModal.features.second.title": "高级调试",
  "communityPlusModal.features.second.description": "轻松修复任何出错的工作流执行，然后重新运行它",
  "communityPlusModal.features.third.title": "执行搜索和标记",
  "communityPlusModal.features.third.description": "搜索和组织过去的执行以方便查看",
  "communityPlusModal.features.fourth.title": "文件夹",
  "communityPlusModal.features.fourth.description": "将工作流程组织在嵌套的文件夹结构中",
  "communityPlusModal.input.email.label": "输入电子邮件以接收您的许可证密钥",
  "communityPlusModal.button.skip": "跳过",
  "communityPlusModal.button.confirm": "向我发送免费许可证密钥",
  "communityPlusModal.notice": "所含功能可能发生变化，但一经解锁，即可永久保留。",
  "executeWorkflowTrigger.createNewSubworkflow": "在 {projectName} 中创建子工作流",
  "executeWorkflowTrigger.createNewSubworkflow.noProject": "创建新的子工作流",
  "evaluation.listRuns.status.new": "新",
  "evaluation.listRuns.status.running": "跑步",
  "evaluation.listRuns.status.evaluating": "评估",
  "evaluation.listRuns.status.completed": "已完成",
  "evaluation.listRuns.status.cancelled": "已取消",
  "evaluation.listRuns.status.error": "错误",
  "evaluation.listRuns.status.success": "成功",
  "evaluation.listRuns.status.warning": "警告",
  "evaluation.listRuns.metricsOverTime": "随时间变化的指标",
  "evaluation.listRuns.status": "状态",
  "evaluation.listRuns.runListHeader": "所有运行",
  "evaluation.listRuns.testCasesListHeader": "运行 #{index}",
  "evaluation.listRuns.runNumber": "跑",
  "evaluation.listRuns.runDate": "运行日期",
  "evaluation.listRuns.runStatus": "运行状态",
  "evaluation.listRuns.noRuns": "没有试运行",
  "evaluation.listRuns.pastRuns.total": "无得分 | 全部得分 | 所有得分",
  "evaluation.listRuns.noRuns.description": "在这里运行测试以查看结果",
  "evaluation.listRuns.deleteRuns": "没有可删除的运行记录 | 删除{count}条运行记录 | 删除{count}条运行记录",
  "evaluation.listRuns.noRuns.button": "运行测试",
  "evaluation.listRuns.toast.error.fetchTestCases": "未能载入运行详情",
  "evaluation.listRuns.error.testCasesNotFound": "数据集中无匹配行{description}",
  "evaluation.listRuns.error.testCasesNotFound.description": "检查评估触发器中设置的任何筛选器或限制",
  "evaluation.listRuns.error.executionInterrupted": "测试运行被中断",
  "evaluation.listRuns.error.unknownError": "执行错误{description}",
  "evaluation.listRuns.error.cantFetchTestRuns": "无法获取测试运行",
  "evaluation.listRuns.error.cantStartTestRun": "未能启动测试运行",
  "evaluation.listRuns.error.cantStopTestRun": "无法停止测试运行",
  "evaluation.listRuns.error.unknownError.description": "点击查看更多详情",
  "evaluation.listRuns.error.evaluationTriggerNotFound": "评估触发器缺失",
  "evaluation.listRuns.error.evaluationTriggerNotConfigured": "评估触发器未配置",
  "evaluation.listRuns.error.evaluationTriggerDisabled": "评估触发器已禁用",
  "evaluation.listRuns.error.setOutputsNodeNotConfigured": "“设置输出”节点未配置",
  "evaluation.listRuns.error.setMetricsNodeNotFound": "工作流中没有“设置指标”节点",
  "evaluation.listRuns.error.setMetricsNodeNotConfigured": "“设置指标”节点未配置",
  "evaluation.listRuns.error.cantFetchTestCases": "无法获取测试案例{description}",
  "evaluation.listRuns.error.cantFetchTestCases.description": "检查评估触发器中的Google表格设置",
  "evaluation.runDetail.ranAt": "跑了",
  "evaluation.runDetail.testCase": "测试用例",
  "evaluation.runDetail.testCase.id": "测试用例编号",
  "evaluation.runDetail.testCase.status": "测试用例状态",
  "evaluation.runDetail.totalCases": "总病例数",
  "evaluation.runDetail.error.mockedNodeMissing": "基准测试执行中未找到模拟节点的输出结果。{link}",
  "evaluation.runDetail.error.mockedNodeMissing.solution": "修复测试配置",
  "evaluation.runDetail.error.executionFailed": "工作流执行失败",
  "evaluation.runDetail.error.executionFailed.solution": "查看执行",
  "evaluation.runDetail.error.datasetTriggerNotFound": "工作流中不存在数据集触发器。{link}",
  "evaluation.runDetail.error.datasetTriggerNotFound.solution": "查看工作流程",
  "evaluation.runDetail.error.invalidMetrics": "评估指标节点返回了无效的指标值。仅允许数值类型数据。请查看工作流程。{link}。",
  "evaluation.runDetail.error.invalidMetrics.solution": "查看工作流程",
  "evaluation.runDetail.error.unknownError": "发生未知错误",
  "evaluation.runDetail.error.unknownError.solution": "查看执行",
  "evaluation.runDetail.error.noMetricsCollected": "未执行“设置指标”节点",
  "evaluation.runDetail.error.partialCasesFailed": "完成但有错误",
  "evaluation.runTest": "运行测试",
  "evaluation.stopTest": "停止测试",
  "evaluation.cancelTestRun": "取消测试运行",
  "evaluation.notImplemented": "该功能尚未实现!",
  "evaluation.viewDetails": "查看详情",
  "evaluation.editTest": "编辑测试",
  "evaluation.deleteTest": "删除测试",
  "evaluation.deleteTest.warning": "测试及所有相关运行记录将被删除。此操作不可撤销。",
  "evaluation.testIsRunning": "测试正在运行。请等待其完成。",
  "evaluation.completeConfig": "请完成以下配置以运行测试：",
  "evaluation.configError.noEvaluationTag": "没有设定评估标签",
  "evaluation.configError.noExecutionsAddedToTag": "此标签下未添加任何执行",
  "evaluation.configError.noEvaluationWorkflow": "无评估流程设置",
  "evaluation.configError.noMetrics": "未设置指标",
  "evaluation.workflowInput.subworkflowName": "{name}的评估流程",
  "evaluation.workflowInput.subworkflowName.default": "我的评估子流程",
  "evaluation.executions.addTo": "添加到测试",
  "evaluation.executions.addTo.new": "添加到测试",
  "evaluation.executions.addTo.existing": "添加到“{name}”",
  "evaluation.executions.addedTo": "已添加到“{name}”",
  "evaluation.executions.removeFrom": '从"{{name}}"中移除',
  "evaluation.executions.removedFrom": "执行已从“{name}”中移除",
  "evaluation.executions.toast.addedTo": "返回至“{name}”",
  "evaluation.executions.tooltip.addTo": "添加到新测试",
  "evaluation.executions.tooltip.noExecutions": "评估执行无法添加到测试中",
  "evaluation.executions.tooltip.onlySuccess": "只有成功执行的（案例）才能被加入测试",
  "evaluation.workflow.createNew": "创建新的评估流程",
  "evaluation.workflow.createNew.or": "或使用现有的评估子工作流程",
  "evaluation.executions.toast.addedTo.title": "测试中添加了执行",
  "evaluation.executions.toast.closeTab": "关闭此标签页",
  "evaluation.executions.toast.removedFrom.title": "测试中移除了执行",
  "evaluations.paywall.title": "注册以启用评估",
  "evaluations.paywall.description": "注册您的社区实例以解锁评估功能",
  "evaluations.paywall.cta": "注册实例",
  "evaluations.setupWizard.title": "测试您的AI工作流程在多种输入下的表现",
  "evaluations.setupWizard.description": "评估通过测试数据集来衡量性能表现。",
  "evaluations.setupWizard.moreInfo": "更多信息",
  "evaluations.setupWizard.stepHeader.optional": "可选",
  "evaluations.setupWizard.step1.title": "连接一个测试数据集",
  "evaluations.setupWizard.step1.item1": "每行一个输入项来设置一个谷歌表格",
  "evaluations.setupWizard.step1.item2": "为你的工作流程添加一个评估触发器并接入系统",
  "evaluations.setupWizard.step1.button": "添加评估触发器",
  "evaluations.setupWizard.step2.title": "将工作流输出写回数据集",
  "evaluations.setupWizard.step2.item1": "添加一个“设置输出”操作，将每个输出记录回Google表格",
  "evaluations.setupWizard.step2.button": "添加“设置输出”节点",
  "evaluations.setupWizard.step3.title": "设置质量评分",
  "evaluations.setupWizard.step3.item1": "计算一个分数，例如通过比较预期输出与实际输出",
  "evaluations.setupWizard.step3.item2": "添加“设置指标”操作以记录分数",
  "evaluations.setupWizard.step3.button": "添加“设置指标”节点",
  "evaluations.setupWizard.step3.skip": "跳过",
  "evaluations.setupWizard.step3.notice": "您的计划仅支持单个工作流程的自定义指标。{link}",
  "evaluations.setupWizard.step3.notice.link": "查看计划",
  "evaluations.setupWizard.step4.title": "运行评估",
  "evaluations.setupWizard.step4.button": "运行评估",
  "evaluations.setupWizard.step4.altButton": "在编辑器中运行",
  "evaluations.setupWizard.limitReached": "已达到限制。您的计划仅支持为一个工作流程设置自定义指标。如需无限制使用，请升级计划，或删除已有评估运行的工作流程。",
  "freeAi.credits.callout.claim.title": "获取 {credits} 个免费 OpenAI API 积分",
  "freeAi.credits.callout.claim.button.label": "申领积分",
  "freeAi.credits.callout.success.title.part1": "已领取 {credits} 个免费OpenAI API额度！请注意这些免费额度仅适用于以下模型：",
  "freeAi.credits.callout.success.title.part2": "GPT-4 Mini、文本嵌入3小型版、DALL·E 3、TTS-1语音合成、Whisper-1语音识别、最新文本审核模型",
  "freeAi.credits.credentials.edit": "这是一个托管的凭据，无法编辑。",
  "freeAi.credits.showError.claim.title": "免费 AI 积分",
  "freeAi.credits.showError.claim.message": "启用以申领积分",
  "freeAi.credits.showWarning.workflow.activation.title": "你正在使用免费的OpenAI API积分",
  "freeAi.credits.showWarning.workflow.activation.description": "为确保您的工作流程今后顺畅运行，请将免费的OpenAI API额度替换为您个人的API密钥。",
  "insights.heading": "洞察",
  "insights.lastNDays": "过去 {count} 天",
  "insights.lastNHours": "最近 {count} 小时",
  "insights.months": "{count} 个月",
  "insights.oneYear": "一年",
  "insights.banner.timeSaved.tooltip": "尚无可用估算。要查看可能节省的时间，请从工作流设置中{link}每个工作流。",
  "insights.banner.timeSaved.tooltip.link.text": "添加时间估算",
  "insights.banner.noData.tooltip": "手动执行不计入统计。升级后数据最长可能需要24小时才能更新完成。{link}",
  "insights.banner.noData.tooltip.link": "了解更多",
  "insights.banner.noData.tooltip.link.url": "https://docs.n8n.io/insights/",
  "insights.banner.title.total": "产品执行",
  "insights.banner.title.failed": "生产执行失败",
  "insights.banner.title.failureRate": "故障率",
  "insights.banner.title.timeSaved": "节省的时间",
  "insights.banner.title.timeSavedDailyAverage": "日均节省时间",
  "insights.banner.title.averageRunTime": "平均运行时间",
  "insights.dashboard.table.projectName": "项目名称",
  "insights.dashboard.table.title": "按工作流细分",
  "insights.dashboard.table.estimate": "估计",
  "insights.dashboard.title": "洞察",
  "insights.dashboard.paywall.title": "升级以获取更详细的分析",
  "insights.dashboard.paywall.description": "获取更精细化的、按工作流程划分的洞察，以及不同时间段内生产执行情况的直观分解。",
  "insights.banner.title.timeSaved.tooltip": "根据所有工作流程中每次执行的预计节省时间计算出的总节省时间",
  "insights.banner.failureRate.deviation.tooltip": "与上期相比的百分点变化",
  "insights.chart.failed": "失败",
  "insights.chart.succeeded": "成功",
  "insights.chart.loading": "正在加载数据",
  "communityNodesDocsLink.link.title": "开放社区节点文档",
  "communityNodesDocsLink.title": "文档",
  "communityNodeItem.node.hint": "安装此节点以开始使用",
  "communityNodeItem.actions.hint": "安装此节点以开始使用操作",
  "communityNodeItem.label": "添加到工作流程",
  "communityNodeDetails.installed": "已安装",
  "communityNodeDetails.install": "安装 Node",
  "communityNodeInfo.approved": "该社区节点已通过n8n审核并批准。",
  "communityNodeInfo.officialApproved": "该节点已通过n8n审核并批准。",
  "communityNodeInfo.approved.label": "已验证",
  "communityNodeInfo.unverified": "该社区节点是通过npm添加的，尚未经过n8n验证。",
  "communityNodeInfo.unverified.label": "通过npm",
  "communityNodeInfo.downloads": "{downloads} 次下载",
  "communityNodeInfo.publishedBy": "由{publisherName}出版",
  "communityNodeInfo.contact.admin": "请联系管理员安装此社区节点：",
  "communityNodeUpdateInfo.available": "一个新版本的节点包可用。",
  "insights.upgradeModal.button.dismiss": "解散",
  "insights.upgradeModal.content": "查看此时间段需要企业版计划。升级至企业版即可解锁高级功能。",
  "insights.upgradeModal.perks.0": "最多查看一年的洞察历史",
  "insights.upgradeModal.perks.1": "放大查看过去24小时，按小时粒度显示",
  "insights.upgradeModal.perks.2": "深入了解工作流程随时间变化的趋势",
  "insights.upgradeModal.title": "升级至企业版",
  "whatsNew.versionsBehind": "落后 {count} 个版本 | 落后 {count} 个版本",
  "whatsNew.update": "更新",
  "whatsNew.updateAvailable": "您当前使用的版本是 {currentVersion}。更新到 {latestVersion} 以获取 {count} 个版本的新功能、改进和修复。查看更新内容。",
  "whatsNew.updateAvailable.changelogLink": "完整变更日志中"
};
export {
  _reusableBaseText,
  _reusableDynamicText,
  zhCN as default,
  error,
  forgotPassword,
  settings,
  startupError
};
