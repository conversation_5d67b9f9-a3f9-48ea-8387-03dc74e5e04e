/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._cardContainer_1ih2c_123 {
  display: flex;
  padding: var(--spacing-s);
  border: var(--border-width-base) var(--border-style-base) var(--color-info-tint-1);
  border-radius: var(--border-radius-large);
  background-color: var(--color-background-xlight);
}
._packageCard_1ih2c_131,
._cardSkeleton_1ih2c_132 {
  display: flex;
  flex-basis: 100%;
  justify-content: space-between;
}
._packageCard_1ih2c_131 {
  align-items: center;
}
._cardSkeleton_1ih2c_132 {
  flex-direction: column;
}
._loader_1ih2c_146 {
  width: 50%;
  transform: scaleY(-1);
}
._loader_1ih2c_146:last-child {
  width: 70%;
}
._loader_1ih2c_146:last-child div {
  margin: 0;
}
._cardInfoContainer_1ih2c_157 {
  display: flex;
  flex-wrap: wrap;
}
._cardTitle_1ih2c_162 {
  flex-basis: 100%;
}
._cardTitle_1ih2c_162 span {
  line-height: 1;
}
._cardSubtitle_1ih2c_169 {
  margin-top: 2px;
  padding-right: var(--spacing-m);
}
._cardControlsContainer_1ih2c_174 {
  display: flex;
  align-items: center;
  gap: var(--spacing-3xs);
}
._cardActions_1ih2c_180 {
  padding-left: var(--spacing-3xs);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._container_1uf27_123 {
  height: 100%;
  padding-right: var(--spacing-2xs);
}
._container_1uf27_123 > * {
  margin-bottom: var(--spacing-2xl);
}
._headingContainer_1uf27_131 {
  display: flex;
  justify-content: space-between;
}
._loadingContainer_1uf27_136 {
  display: flex;
  gap: var(--spacing-xs);
}
._actionBoxContainer_1uf27_141 {
  text-align: center;
}
._cardsContainer_1uf27_145 {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2xs);
}