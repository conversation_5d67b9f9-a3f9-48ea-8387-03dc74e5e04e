/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._indent_1jh97_123 {
  margin-left: var(--spacing-s);
}
._simple_1jh97_127 {
  text-indent: calc(var(--spacing-s) * -1);
  margin-left: var(--spacing-s);
  max-width: 300px;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._dataDisplay_7vm96_123 {
  position: absolute;
  top: 0;
  left: 0;
  padding-left: var(--spacing-xs);
  right: 0;
  overflow-y: auto;
  line-height: 1.5;
  word-break: normal;
  height: 100%;
  padding-bottom: var(--spacing-3xl);
}
._dataDisplay_7vm96_123._compact_7vm96_135 {
  padding-left: var(--spacing-2xs);
}
._table_7vm96_139 {
  border-collapse: separate;
  text-align: left;
  width: 100%;
  font-size: var(--font-size-2xs);
  color: var(--color-text-base);
}
._table_7vm96_139 th {
  background-color: var(--color-background-base);
  border-top: var(--border-base);
  border-bottom: var(--border-base);
  border-left: var(--border-base);
  position: sticky;
  top: 0;
  color: var(--color-text-dark);
  z-index: 1;
}
._lightHeader_7vm96_156 ._table_7vm96_139 th {
  background-color: var(--color-background-light);
}
._table_7vm96_139 th._tableRightMargin_7vm96_159 {
  background-color: transparent;
}
._table_7vm96_139 td {
  vertical-align: top;
  padding: var(--spacing-4xs) var(--spacing-3xs);
  border-bottom: var(--border-base);
  border-left: var(--border-base);
  overflow-wrap: break-word;
  white-space: pre-wrap;
  vertical-align: top;
}
._table_7vm96_139 td:first-child,
._table_7vm96_139 td:nth-last-child(2) {
  position: relative;
  z-index: 0;
}
._table_7vm96_139 td:first-child:after,
._table_7vm96_139 td:nth-last-child(2):after {
  content: "";
  position: absolute;
  height: 100%;
  width: 2px;
  top: 0;
}
._table_7vm96_139 td:nth-last-child(2):after {
  right: -1px;
}
._table_7vm96_139 td:first-child:after {
  left: -1px;
}
._table_7vm96_139 th:last-child,
._table_7vm96_139 td:last-child {
  border-right: var(--border-base);
}
._hasCollapsingColumn_7vm96_194 ._table_7vm96_139 {
  table-layout: fixed;
}
._hasCollapsingColumn_7vm96_194 ._table_7vm96_139 td:not(._isCollapsingColumn_7vm96_197) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
._hasCollapsingColumn_7vm96_194 ._table_7vm96_139 td:not(._isCollapsingColumn_7vm96_197) .n8n-tree {
  height: 1.5em;
  overflow: hidden;
}
th._isCollapsingColumn_7vm96_197 {
  border-top-color: var(--color-foreground-xdark);
  border-left-color: var(--color-foreground-xdark);
  border-right-color: var(--color-foreground-xdark);
}
td._isCollapsingColumn_7vm96_197 {
  border-left-color: var(--color-foreground-xdark);
  border-right-color: var(--color-foreground-xdark);
}
tr:last-child td._isCollapsingColumn_7vm96_197 {
  border-bottom-color: var(--color-foreground-xdark);
}
td._isCollapsingColumn_7vm96_197 + td,
th._isCollapsingColumn_7vm96_197 + th {
  border-left-color: var(--color-foreground-xdark);
}
._nodeClass_7vm96_226 {
  margin-bottom: var(--spacing-5xs);
}
._emptyCell_7vm96_230 {
  height: 32px;
}
._header_7vm96_234 {
  display: flex;
  align-items: center;
  padding: var(--spacing-4xs) var(--spacing-3xs);
}
._header_7vm96_234 span {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  flex-grow: 1;
}
._draggableHeader_7vm96_246:hover {
  cursor: grab;
  background-color: var(--color-foreground-base);
}
._draggableHeader_7vm96_246:hover ._dragButton_7vm96_250 {
  opacity: 1;
}
._highlight_7vm96_254 ._draggableHeader_7vm96_246 {
  color: var(--color-primary);
}
._draggingHeader_7vm96_258 {
  color: var(--color-primary);
  background-color: var(--color-primary-tint-2);
}
._activeHeader_7vm96_263 ._dragButton_7vm96_250 {
  opacity: 1;
}
._dragButton_7vm96_250 {
  opacity: 0;
}
._dragButton_7vm96_250 > svg {
  vertical-align: middle;
}
._dataKey_7vm96_274 {
  color: var(--color-text-dark);
  line-height: 1.7;
  font-weight: var(--font-weight-bold);
  border-radius: var(--border-radius-base);
  padding: 0 var(--spacing-5xs) 0 var(--spacing-5xs);
  margin-right: var(--spacing-5xs);
}
._value_7vm96_283 {
  line-height: var(--font-line-height-regular);
}
._nestedValue_7vm96_287 {
  margin-left: var(--spacing-4xs);
}
._mappable_7vm96_292 {
  cursor: grab;
}
._empty_7vm96_230 {
  color: var(--color-danger);
}
._limitColWidth_7vm96_300 {
  max-width: 300px;
}
._minColWidth_7vm96_304 {
  min-width: 240px;
}
._hoveringKey_7vm96_308 {
  background-color: var(--color-foreground-base);
}
._draggingKey_7vm96_312 {
  background-color: var(--color-primary-tint-2);
}
._tableRightMargin_7vm96_159 {
  width: var(--ndv-spacing);
  border-right: none !important;
  border-top: none !important;
  border-bottom: none !important;
}
._compact_7vm96_135 ._tableRightMargin_7vm96_159 {
  padding: 0;
  min-width: var(--spacing-2xs);
  max-width: var(--spacing-2xs);
}
._hoveringRow_7vm96_328 td:first-child:after,
._hoveringRow_7vm96_328 td:nth-last-child(2):after {
  background-color: var(--color-secondary);
}
._warningTooltip_7vm96_333 {
  color: var(--color-warning);
}
._executionLinkCell_7vm96_337 {
  padding: var(--spacing-3xs) !important;
}
._executionLinkRowHeader_7vm96_341 {
  width: var(--spacing-m);
}
._collapseColumnButton_7vm96_345 {
  opacity: 0;
  margin-block: calc(-2 * var(--spacing-2xs));
}
._collapseColumnButton_7vm96_345 span {
  flex-shrink: 0;
}
._isCollapsingColumn_7vm96_197 ._collapseColumnButton_7vm96_345, th._isHoveredColumn_7vm96_352 ._collapseColumnButton_7vm96_345, th:hover ._collapseColumnButton_7vm96_345 {
  opacity: 1;
}