/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._item_1amfi_123 {
  display: flex;
  position: relative;
  align-items: center;
  justify-content: space-between;
  border-left: 2px var(--border-style-base) transparent;
  border-bottom: var(--border-width-base) var(--border-style-base) var(--color-foreground-base);
  color: var(--color-text-base);
  font-size: var(--font-size-2xs);
}
._item_1amfi_123 p {
  display: grid;
  padding: var(--spacing-s);
  cursor: pointer;
  flex: 1 1 auto;
}
._item_1amfi_123 p time {
  padding: 0 0 var(--spacing-5xs);
  color: var(--color-text-dark);
  font-size: var(--font-size-s);
  font-weight: var(--font-weight-bold);
}
._item_1amfi_123 p span,
._item_1amfi_123 p data {
  justify-self: start;
  max-width: 160px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: calc(var(--spacing-4xs) * -1);
  font-size: var(--font-size-2xs);
}
._item_1amfi_123 ._tail_1amfi_155 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
._item_1amfi_123._active_1amfi_160 {
  background-color: var(--color-background-base);
  border-left-color: var(--color-primary);
}
._item_1amfi_123._active_1amfi_160 p {
  cursor: default;
}
._item_1amfi_123:hover, ._item_1amfi_123._actionsVisible_1amfi_167 {
  border-left-color: var(--color-foreground-xdark);
}
._actions_1amfi_167 {
  display: block;
  padding: var(--spacing-3xs);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._list_1rv1j_123 {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
}
._empty_1rv1j_132 {
  display: flex;
  position: absolute;
  height: 100%;
  padding: 0 25%;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: var(--color-text-base);
  font-size: var(--font-size-s);
  line-height: var(--font-line-height-loose);
}
._loader_1rv1j_145 {
  padding: 0 var(--spacing-s);
}
._retention_1rv1j_149 {
  display: grid;
  padding: var(--spacing-s);
  font-size: var(--font-size-2xs);
  line-height: var(--font-line-height-loose);
  text-align: center;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._content_ne8yf_123 {
  position: absolute;
  display: block;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
}
._info_ne8yf_133 {
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
  width: 100%;
}
._card_ne8yf_141 {
  padding: var(--spacing-s) var(--spacing-l) 0 var(--spacing-xl);
  border: 0;
  align-items: start;
}
._card_ne8yf_141 ._text_ne8yf_146 {
  display: flex;
  flex-direction: column;
  flex: 1 1 auto;
}
._card_ne8yf_141 ._text_ne8yf_146 p {
  display: flex;
  align-items: center;
  padding: 0;
  cursor: default;
}
._card_ne8yf_141 ._text_ne8yf_146 p:first-child {
  padding-top: var(--spacing-3xs);
  padding-bottom: var(--spacing-4xs);
}
._card_ne8yf_141 ._text_ne8yf_146 p:first-child * {
  margin-top: auto;
  font-size: var(--font-size-m);
}
._card_ne8yf_141 ._text_ne8yf_146 p:last-child {
  padding-top: var(--spacing-3xs);
}
._card_ne8yf_141 ._text_ne8yf_146 p:last-child * {
  font-size: var(--font-size-2xs);
}
._card_ne8yf_141 ._text_ne8yf_146 p ._label_ne8yf_171 {
  color: var(--color-text-light);
  padding-right: var(--spacing-4xs);
}
._card_ne8yf_141 ._text_ne8yf_146 p * {
  max-width: unset;
  justify-self: unset;
  white-space: unset;
  overflow: hidden;
  text-overflow: unset;
  padding: 0;
  font-size: var(--font-size-s);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._view_1m8fw_123 {
  position: relative;
  display: grid;
  width: 100%;
  grid-template-areas: "header corner" "content list";
  grid-template-columns: auto 330px;
  grid-template-rows: 65px auto;
  background-color: var(--color-background-xlight);
}
._header_1m8fw_133 {
  grid-area: header;
  display: flex;
  align-items: center;
  padding: 0 var(--spacing-l);
  border-bottom: var(--border-width-base) var(--border-style-base) var(--color-foreground-base);
}
._corner_1m8fw_141 {
  grid-area: corner;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-3xs) 0 var(--spacing-s);
  background-color: var(--color-background-lighter);
  border-bottom: var(--border-width-base) var(--border-style-base) var(--color-foreground-base);
  border-left: var(--border-width-base) var(--border-style-base) var(--color-foreground-base);
}
._contentComponentWrapper_1m8fw_152 {
  grid-area: content;
  position: relative;
}
._listComponentWrapper_1m8fw_157 {
  grid-area: list;
  position: relative;
}
._listComponentWrapper_1m8fw_157::before {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: var(--border-width-base);
  background-color: var(--color-foreground-base);
}