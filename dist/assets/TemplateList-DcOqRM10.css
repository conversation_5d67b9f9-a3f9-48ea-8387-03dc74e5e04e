/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._list_1a0mh_123 {
  max-width: 100px;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
}
._container_1a0mh_131 {
  position: relative;
  display: block;
}
._sm_1a0mh_136 {
  margin-left: var(--spacing-2xs);
}
._md_1a0mh_140 {
  margin-left: var(--spacing-xs);
}
._button_1a0mh_144 {
  top: 0px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  background: var(--color-background-light);
  border: 1px var(--color-foreground-base) solid;
  border-radius: var(--border-radius-base);
  font-size: 10px;
  font-weight: var(--font-weight-bold);
  color: var(--color-text-base);
}
._buttonSm_1a0mh_158 {
  margin-left: var(--spacing-2xs);
  width: 20px;
  min-width: 20px;
  height: 20px;
}
._buttonMd_1a0mh_165 {
  margin-left: var(--spacing-xs);
  width: 24px;
  min-width: 24px;
  height: 24px;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._nodes_1xbou_123 {
  display: flex;
  justify-content: center;
  align-content: center;
  flex-direction: row;
}
._icon_1xbou_130 {
  margin-left: var(--spacing-xs);
}
._card_1xbou_134 {
  position: relative;
  border-left: var(--border-base);
  border-right: var(--border-base);
  border-bottom: var(--border-base);
  background-color: var(--color-background-xlight);
  display: flex;
  align-items: center;
  padding: 0 var(--spacing-s) var(--spacing-s) var(--spacing-s);
  background-color: var(--color-background-xlight);
  cursor: pointer;
}
._card_1xbou_134:hover ._hideOnHover_1xbou_146 {
  visibility: hidden;
}
._card_1xbou_134:hover ._buttonContainer_1xbou_149 {
  display: block;
}
._buttonContainer_1xbou_149 {
  display: none;
  position: absolute;
  right: 10px;
  top: 30%;
}
._loaded_1xbou_160 {
  padding-top: var(--spacing-s);
}
._first_1xbou_164 {
  border-top: var(--border-base);
  border-top-right-radius: var(--border-radius-large);
  border-top-left-radius: var(--border-radius-large);
}
._last_1xbou_170 {
  border-bottom-right-radius: var(--border-radius-large);
  border-bottom-left-radius: var(--border-radius-large);
}
._content_1xbou_175 {
  display: flex;
  align-items: center;
}
._line_1xbou_180 {
  padding: 0 6px;
  color: var(--color-foreground-base);
  font-size: var(--font-size-2xs);
}
._loading_1xbou_186 {
  width: 100%;
  background-color: var(--color-background-xlight);
}
._nodesContainer_1xbou_191 {
  min-width: 175px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  flex-grow: 1;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._header_ryymc_123 {
  padding-bottom: var(--spacing-2xs);
}
._workflowButton_ryymc_127:hover ._button_ryymc_127 {
  display: block;
}
._workflowButton_ryymc_127:hover ._nodes_ryymc_130 {
  display: none;
}