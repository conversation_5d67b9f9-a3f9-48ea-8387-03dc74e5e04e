/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._wrapper_1x8n3_123 {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  max-width: var(--content-container-width);
  box-sizing: border-box;
  align-content: start;
  padding: var(--spacing-l) var(--spacing-2xl) 0;
}
@media screen and (max-width: 991px) {
._wrapper_1x8n3_123 {
    padding: var(--spacing-s) var(--spacing-s) 0;
}
}
._content_1x8n3_139 {
  display: grid;
  height: 100%;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._wrapper_hn9dc_123 {
  display: grid;
  height: 100%;
}
._overflow_hn9dc_128 ._list_hn9dc_128 ._body_hn9dc_128 {
  overflow: auto;
}
._list_hn9dc_128 {
  display: grid;
  grid-template-rows: auto 1fr;
  height: 100%;
}
._list_hn9dc_128 ._body_hn9dc_128 {
  position: relative;
  height: 100%;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._filter-button_1llux_123 {
  height: 30px;
  align-items: center;
}
._filter-button_1llux_123._no-label_1llux_127 {
  width: 30px;
}
._filter-button_1llux_123._no-label_1llux_127 span + span {
  margin: 0;
}
._filter-button_1llux_123 ._filter-button-count_1llux_133 {
  margin-right: var(--spacing-4xs);
}
@media screen and (max-width: 767px) {
._filter-button_1llux_123 ._filter-button-count_1llux_133 {
    margin-right: 0;
}
}
@media screen and (max-width: 480px) {
._filter-button_1llux_123 ._filter-button-text_1llux_142 {
    text-indent: -10000px;
}
._filter-button_1llux_123 span + span {
    margin: 0;
}
}
._filters-dropdown_1llux_150 {
  width: 280px;
}
._filters-dropdown-footer_1llux_154 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._filters-row_a3x6d_123 {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: var(--spacing-2xs);
}
._filters_a3x6d_123 {
  display: grid;
  grid-auto-flow: column;
  grid-auto-columns: 1fr max-content max-content max-content;
  gap: var(--spacing-4xs);
  align-items: center;
  justify-content: end;
  width: 100%;
}
._filters_a3x6d_123 ._sort-and-filter_a3x6d_141 {
  display: flex;
  gap: var(--spacing-4xs);
  align-items: center;
}
@media screen and (max-width: 767px) {
._filters_a3x6d_123 {
    grid-auto-flow: row;
    grid-auto-columns: unset;
    grid-template-columns: 1fr;
}
}
._search_a3x6d_154 {
  max-width: 196px;
  justify-self: end;
}
._search_a3x6d_154 input {
  height: 30px;
}
@media screen and (max-width: 991px) {
._search_a3x6d_154 {
    max-width: 100%;
}
}
._listWrapper_a3x6d_168 {
  position: absolute;
  height: 100%;
  width: 100%;
}
._paginatedListWrapper_a3x6d_174 {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: auto;
  gap: var(--spacing-m);
}
._listPagination_a3x6d_182 {
  display: flex;
  justify-content: flex-end;
  margin-bottom: var(--spacing-l);
}
._listPagination_a3x6d_182 .el-pagination__sizes {
  height: 100%;
  position: relative;
  top: -1px;
}
._listPagination_a3x6d_182 .el-pagination__sizes input {
  height: 100%;
  min-height: 28px;
}
._listPagination_a3x6d_182 .el-pagination__sizes .el-input__suffix {
  width: var(--spacing-m);
}
._sort-and-filter_a3x6d_141 {
  white-space: nowrap;
}
@media screen and (max-width: 991px) {
._sort-and-filter_a3x6d_141 {
    width: 100%;
}
}
._datatable_a3x6d_209 {
  padding-bottom: var(--spacing-s);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
.resource-list-loading[data-v-5a4d0558] {
  position: relative;
  height: 0;
  width: 100%;
  overflow: hidden;
  /*
  Show the loading skeleton only if the loading takes longer than 300ms
  */
  animation: 0.01s linear 0.3s forwards changeVisibility-5a4d0558;
}
.resource-list-loading[data-v-5a4d0558] .el-skeleton {
  position: absolute;
  height: 100%;
  width: 100%;
  overflow: hidden;
}
.resource-list-loading[data-v-5a4d0558] .el-skeleton > div > div:first-child .el-skeleton__item {
  height: 42px;
  margin: 0;
}
.resource-list-loading[data-v-5a4d0558] .el-skeleton .el-skeleton__item {
  height: 69px;
}
.resource-list-loading-instant[data-v-5a4d0558] {
  animation: 0.01s linear 0s forwards changeVisibility-5a4d0558;
}
@keyframes changeVisibility-5a4d0558 {
from {
    height: 0;
}
to {
    height: 100%;
}
}