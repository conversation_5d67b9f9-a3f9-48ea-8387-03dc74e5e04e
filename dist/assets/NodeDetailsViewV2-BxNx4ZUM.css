
._ndvHeader_q9139_2 {
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: space-between;
	gap: var(--spacing-2xs);
	padding: var(--spacing-2xs);
	background: var(--color-background-xlight);
}
._content_q9139_12 {
	display: flex;
	align-items: flex-end;
	gap: var(--spacing-2xs);
	margin-left: var(--spacing-2xs);
}
._title_q9139_19 {
	color: var(--color-text-dark);
	font-size: var(--font-size-m);
}
._subtitle_q9139_24 {
	display: flex;
	align-items: baseline;
	gap: var(--spacing-2xs);
	margin: 0;
}
._docsLabel_q9139_31 {
	display: flex;
	gap: var(--spacing-4xs);
}
._icon_q9139_36 {
	align-self: center;
	z-index: 1;
}
/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._dragButton_7p0lm_123 {
  cursor: ew-resize;
  border: none;
  outline: none;
  background: var(--color-background-xlight);
  display: flex;
  align-items: baseline;
  gap: var(--spacing-2xs);
  padding: var(--spacing-4xs) var(--spacing-2xs) var(--spacing-4xs) var(--spacing-2xs);
  color: var(--color-foreground-dark);
  border: var(--border-base);
  border-bottom: none;
  border-top-left-radius: var(--border-radius-base);
  border-top-right-radius: var(--border-radius-base);
}
._dragButton_7p0lm_123 ._arrow_7p0lm_138 {
  opacity: 0;
  width: 7px;
}
._dragButton_7p0lm_123 ._handle_7p0lm_142 {
  width: 11px;
  transform: rotate(90deg);
}
._dragButton_7p0lm_123:hover ._arrow_7p0lm_138, ._dragButton_7p0lm_123._dragging_7p0lm_146 ._arrow_7p0lm_138 {
  opacity: 1;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._backdrop_49khn_123 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-ndv-overlay-background);
}
._dialog_49khn_132 {
  position: fixed;
  width: calc(100vw - var(--spacing-2xl));
  height: calc(100vh - var(--spacing-2xl));
  top: var(--spacing-l);
  left: var(--spacing-l);
  border: none;
  background: none;
  padding: 0;
  margin: 0;
  display: flex;
}
._container_49khn_145 {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  background: var(--border-color-base);
  border: var(--border-base);
  border-radius: var(--border-radius-large);
  color: var(--color-text-base);
  min-width: 0;
}
._main_49khn_156 {
  width: 0;
  flex-grow: 1;
  display: flex;
  align-items: stretch;
}
._column_49khn_163 {
  min-width: 0;
}
._column_49khn_163 + ._column_49khn_163 {
  border-left: var(--border-base);
}
._column_49khn_163:first-child > div {
  border-bottom-left-radius: var(--border-radius-large);
}
._column_49khn_163:last-child {
  border-bottom-right-radius: var(--border-radius-large);
}
._input_49khn_176,
._output_49khn_177 {
  min-width: 280px;
}
._dataColumn_49khn_181 {
  overflow-x: auto;
}
._header_49khn_185 {
  border-bottom: var(--border-base);
  border-top-left-radius: var(--border-radius-large);
  border-top-right-radius: var(--border-radius-large);
}
._main_49khn_156 {
  display: flex;
  width: 100%;
  height: 100%;
  min-height: 0;
  position: relative;
}
._settings_49khn_199 {
  overflow: hidden;
  flex-grow: 1;
}
._draggable_49khn_204 {
  --draggable-height: 22px;
  position: absolute;
  top: calc(-1 * var(--draggable-height));
  left: 50%;
  transform: translateX(-50%);
  height: var(--draggable-height);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
[data-has-output-connection=true] .ndv-connection-hint-notice {
  display: none;
}