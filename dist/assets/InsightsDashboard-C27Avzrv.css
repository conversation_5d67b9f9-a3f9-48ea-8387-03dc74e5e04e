
.perks-list {
&[data-v-f995e7d0] {
	margin: 0;
	padding: 0;
	list-style: none;
	display: flex;
	flex-direction: column;
	gap: var(--spacing-s);
}
> li[data-v-f995e7d0] {
		display: flex;
		align-items: center;
		gap: var(--spacing-2xs);
}
}
/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._insightsView_8g4zl_123 {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 30px;
  overflow: auto;
}
._insightsContainer_8g4zl_131 {
  width: 100%;
  max-width: var(--content-container-width);
  padding: var(--spacing-l) var(--spacing-2xl);
  margin: 0 auto;
}
._insightsBanner_8g4zl_138 {
  padding-bottom: 0;
}
._insightsBanner_8g4zl_138 ul {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}
._insightsContent_8g4zl_146 {
  padding: var(--spacing-l) 0;
  border: var(--border-width-base) var(--border-style-base) var(--color-foreground-base);
  border-top: 0;
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
  background: var(--color-background-xlight);
}
._insightsContentWrapper_8g4zl_155 {
  position: relative;
  overflow-x: hidden;
}
._insightsChartWrapper_8g4zl_160 {
  position: relative;
  height: 292px;
  padding: 0 var(--spacing-l);
  z-index: 1;
}
._insightsTableWrapper_8g4zl_167 {
  position: relative;
  padding: var(--spacing-l) var(--spacing-l) 0;
  z-index: 1;
}
._dataLoader_8g4zl_173 {
  position: absolute;
  top: 0;
  left: -100%;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 9px;
  z-index: 2;
}
._dataLoader_8g4zl_173._isDataLoading_8g4zl_186 {
  transition: left 0s linear;
  left: 0;
  transition-delay: 0.5s;
}
._dataLoader_8g4zl_173 > span {
  position: relative;
  z-index: 2;
}
._dataLoader_8g4zl_173::before {
  content: "";
  position: absolute;
  display: block;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--color-background-xlight);
  opacity: 0.75;
  z-index: 1;
}