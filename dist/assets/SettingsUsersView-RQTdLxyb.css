/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._roleLabel_xwvmw_123 {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-3xs);
  background: transparent;
  padding: 0;
  border: none;
  cursor: pointer;
}
._radioLabel_xwvmw_133 {
  max-width: 268px;
  display: inline-flex;
  flex-direction: column;
  padding: var(--spacing-2xs) 0;
}
._radioLabel_xwvmw_133 span {
  white-space: normal;
}
._removeUser_xwvmw_143 {
  display: block;
  padding: var(--spacing-2xs) var(--spacing-l);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._projects_1clp6_123 {
  display: flex;
  height: 100%;
  align-items: center;
}
._project_1clp6_123 {
  flex: 1 1 0;
  min-width: 0;
  max-width: max-content;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
._comma_1clp6_138 {
  flex-shrink: 0;
  padding: 0 var(--spacing-5xs);
}
._projectList_1clp6_143 {
  padding: 0 var(--spacing-s);
}
._projectList_1clp6_143 li {
  list-style: disc outside;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._userCount_1dht1_123 {
  display: block;
  padding: var(--spacing-3xs) 0 0;
}
._buttonContainer_1dht1_128 {
  display: flex;
  justify-content: space-between;
  gap: var(--spacing-s);
  margin: 0 0 var(--spacing-s);
}
._search_1dht1_135 {
  max-width: 300px;
}
._setupInfoContainer_1dht1_139 {
  max-width: 728px;
}
._settingsContainer_1dht1_143 {
  display: flex;
  align-items: center;
  padding-left: var(--spacing-s);
  margin-bottom: var(--spacing-l);
  justify-content: space-between;
  flex-shrink: 0;
  border-radius: 4px;
  border: 1px solid var(--Colors-Foreground---color-foreground-base, #d9dee8);
}
._settingsContainerInfo_1dht1_154 {
  display: flex;
  padding: 8px 0px;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: 1px;
}
._settingsContainerAction_1dht1_163 {
  display: flex;
  padding: 20px 16px 20px 248px;
  justify-content: flex-end;
  align-items: center;
  flex-shrink: 0;
}
._container_1dht1_171 {
  padding-bottom: 20px;
}