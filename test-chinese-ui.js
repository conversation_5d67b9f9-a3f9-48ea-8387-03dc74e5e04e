const { chromium } = require('playwright');

async function testN8nChineseUI() {
    console.log('🇨🇳 开始测试 n8n 中文界面...');
    
    // 启动浏览器
    const browser = await chromium.launch({ 
        headless: false,  // 显示浏览器窗口
        slowMo: 1000     // 每个操作间隔1秒，便于观察
    });
    
    const context = await browser.newContext({
        // 设置浏览器语言为中文
        locale: 'zh-CN',
        extraHTTPHeaders: {
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
        }
    });
    
    const page = await context.newPage();
    
    try {
        console.log('📱 访问 n8n 登录页面...');
        await page.goto('http://localhost:5678');
        
        // 等待页面加载
        await page.waitForLoadState('networkidle');
        
        console.log('📸 截图：登录页面');
        await page.screenshot({ path: 'screenshots/01-login-page.png', fullPage: true });
        
        // 检查登录页面的中文元素
        console.log('🔍 检查登录页面中文元素...');
        
        // 查找可能的中文文本
        const chineseElements = await page.locator('text=/[\\u4e00-\\u9fff]+/').all();
        console.log(`找到 ${chineseElements.length} 个包含中文的元素`);
        
        for (let i = 0; i < chineseElements.length; i++) {
            const text = await chineseElements[i].textContent();
            console.log(`  中文文本 ${i + 1}: "${text}"`);
        }
        
        // 检查页面标题
        const title = await page.title();
        console.log(`页面标题: ${title}`);
        
        // 检查是否有语言选择器
        const languageSelector = page.locator('[data-test-id="language-selector"], .language-selector, select[name*="language"], select[name*="locale"]');
        if (await languageSelector.count() > 0) {
            console.log('✅ 找到语言选择器');
            await page.screenshot({ path: 'screenshots/02-language-selector.png' });
        } else {
            console.log('❌ 未找到语言选择器');
        }
        
        // 尝试查找登录表单的中文标签
        const emailLabel = page.locator('label:has-text("邮箱"), label:has-text("电子邮件"), label:has-text("Email")');
        const passwordLabel = page.locator('label:has-text("密码"), label:has-text("Password")');
        const signInButton = page.locator('button:has-text("登录"), button:has-text("登入"), button:has-text("Sign in")');
        
        if (await emailLabel.count() > 0) {
            const emailText = await emailLabel.first().textContent();
            console.log(`✅ 邮箱标签: "${emailText}"`);
        }
        
        if (await passwordLabel.count() > 0) {
            const passwordText = await passwordLabel.first().textContent();
            console.log(`✅ 密码标签: "${passwordText}"`);
        }
        
        if (await signInButton.count() > 0) {
            const buttonText = await signInButton.first().textContent();
            console.log(`✅ 登录按钮: "${buttonText}"`);
        }
        
        // 检查是否有"忘记密码"链接
        const forgotPassword = page.locator('a:has-text("忘记密码"), a:has-text("Forgot"), text=/忘记.*密码/');
        if (await forgotPassword.count() > 0) {
            const forgotText = await forgotPassword.first().textContent();
            console.log(`✅ 忘记密码链接: "${forgotText}"`);
        }
        
        // 尝试检查页面的语言设置
        const htmlLang = await page.getAttribute('html', 'lang');
        console.log(`HTML lang 属性: ${htmlLang || '未设置'}`);
        
        // 检查浏览器控制台中的语言相关错误
        console.log('🔍 检查控制台消息...');
        const consoleLogs = [];
        page.on('console', msg => {
            consoleLogs.push(`${msg.type()}: ${msg.text()}`);
        });
        
        // 等待一下让控制台消息收集完整
        await page.waitForTimeout(2000);
        
        // 输出控制台消息
        consoleLogs.forEach(log => {
            if (log.includes('locale') || log.includes('language') || log.includes('zh-CN') || log.includes('i18n')) {
                console.log(`📝 相关控制台消息: ${log}`);
            }
        });
        
        // 尝试检查网络请求中的语言包加载
        console.log('🌐 监控语言包加载请求...');
        page.on('response', response => {
            const url = response.url();
            if (url.includes('locale') || url.includes('zh-CN') || url.includes('language') || url.includes('i18n')) {
                console.log(`📦 语言相关请求: ${response.status()} - ${url}`);
            }
        });
        
        // 刷新页面以触发语言包加载
        await page.reload();
        await page.waitForLoadState('networkidle');
        
        console.log('📸 截图：刷新后的页面');
        await page.screenshot({ path: 'screenshots/03-after-reload.png', fullPage: true });
        
        // 最终检查
        console.log('📋 最终检查结果:');
        console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
        
        const finalChineseElements = await page.locator('text=/[\\u4e00-\\u9fff]+/').all();
        if (finalChineseElements.length > 0) {
            console.log(`✅ 页面包含 ${finalChineseElements.length} 个中文元素`);
            for (let i = 0; i < Math.min(finalChineseElements.length, 5); i++) {
                const text = await finalChineseElements[i].textContent();
                console.log(`   "${text}"`);
            }
        } else {
            console.log('❌ 页面未发现中文文本');
            console.log('💡 可能的原因:');
            console.log('   1. 中文语言包未正确加载');
            console.log('   2. 需要在用户设置中手动切换语言');
            console.log('   3. 浏览器语言设置未生效');
        }
        
        console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
        
    } catch (error) {
        console.error('❌ 测试过程中出现错误:', error);
    } finally {
        console.log('🔚 测试完成，浏览器将在5秒后关闭...');
        await page.waitForTimeout(5000);
        await browser.close();
    }
}

// 运行测试
testN8nChineseUI().catch(console.error);
